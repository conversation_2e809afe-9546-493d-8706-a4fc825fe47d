"""Reports commands for the CLI."""

from datetime import datetime
from typing import Optional
from uuid import UUID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import (
    REPORT_ADJUSTER_PERFORMANCE_PATH,
    REPORT_CLAIMS_BY_STATUS_PATH,
    REPORT_CLAIMS_BY_TYPE_PATH,
    REPORT_CLAIMS_KPIS_PATH,
    REPORT_CLAIMS_OVER_TIME_PATH,
    REPORT_FINANCIAL_KPIS_PATH,
    REPORT_PAYMENTS_VS_RESERVES_PATH,
    REPORTS_PATH,
)
from claimentine_cli.utils.formatting import format_date, format_decimal, format_percentage

app = typer.Typer(help="Report generation commands")
console = Console()

# Common option parameters for reuse
PERIOD_OPTION = typer.Option(
    None, "--period", help="Time period (last_30_days, last_6_months, last_1_year)"
)
START_DATE_OPTION = typer.Option(
    None, "--start-date", help="Start date (ISO format, e.g., 2023-06-01)"
)
END_DATE_OPTION = typer.Option(None, "--end-date", help="End date (ISO format, e.g., 2023-06-30)")
COMPARE_PERIOD_OPTION = typer.Option(
    False, "--compare-period/--no-compare-period", help="Compare with previous period"
)
CUSTOMER_ID_OPTION = typer.Option(None, "--customer-id", help="Filter by customer ID")
CLAIM_TYPE_OPTION = typer.Option(
    None, "--claim-type", help="Filter by claim type (AUTO, PROPERTY, GENERAL_LIABILITY)"
)
USER_ID_OPTION = typer.Option(None, "--user-id", help="Filter by user ID")


def _build_report_params(
    period: Optional[str],
    start_date: Optional[str],
    end_date: Optional[str],
    compare_period: bool,
    customer_id: Optional[UUID],
    claim_type: Optional[str],
    user_id: Optional[UUID],
) -> dict:
    """Build query parameters for report API requests."""
    params = {}

    if period:
        params["period"] = period
    if start_date:
        params["start_date"] = start_date
    if end_date:
        params["end_date"] = end_date
    if compare_period:
        params["compare_period"] = compare_period
    if customer_id:
        params["customer_id"] = str(customer_id)
    if claim_type:
        params["claim_type"] = claim_type
    if user_id:
        params["user_id"] = str(user_id)

    return params


def _display_report_metadata(metadata: dict) -> None:
    """Display common report metadata."""
    console.print(f"[bold cyan]Report:[/bold cyan] {metadata['report_name']}")
    console.print(f"[cyan]Generated at:[/cyan] {metadata['generated_at']}")

    filters = metadata.get("filters_applied", {})
    if filters:
        console.print("[cyan]Filters applied:[/cyan]")
        for key, value in filters.items():
            if value:
                console.print(f"  - {key}: {value}")

    console.print("")  # Empty line for spacing


@app.command(name="claims-kpis")
def claims_kpi_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Claims KPI report with key performance indicators.

    Shows total claims, open/closed percentages, and resolution times.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_CLAIMS_KPIS_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display KPI data
    data = report["data"]

    kpi_table = Table(title="Claims KPIs")
    kpi_table.add_column("Metric", style="cyan")
    kpi_table.add_column("Value", style="green")
    kpi_table.add_column("Change", style="yellow")

    kpi_table.add_row(
        "Total Claims", str(data["total_claims"]), _format_change(data.get("total_claims_change"))
    )

    kpi_table.add_row(
        "Open Claims",
        f"{data['open_claims']} ({format_percentage(data['open_claims_percentage'])})",
        "",
    )

    kpi_table.add_row(
        "Closed Claims",
        f"{data['closed_claims']} ({format_percentage(data['closed_claims_percentage'])})",
        "",
    )

    avg_resolution = data.get("avg_resolution_time_days")
    if avg_resolution is not None:
        resolution_str = f"{avg_resolution:.1f} days"
    else:
        resolution_str = "N/A"

    kpi_table.add_row(
        "Average Resolution Time",
        resolution_str,
        _format_change(data.get("avg_resolution_time_days_change")),
    )

    console.print(kpi_table)


@app.command(name="claims-by-type")
def claims_by_type_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Claims by Type report.

    Shows distribution of claims across different claim types.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_CLAIMS_BY_TYPE_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display claims by type data
    type_table = Table(title="Claims by Type")
    type_table.add_column("Claim Type", style="cyan")
    type_table.add_column("Count", style="green", justify="right")
    type_table.add_column("Percentage", style="yellow", justify="right")

    for item in report["data"]:
        type_table.add_row(
            item["claim_type"], str(item["count"]), format_percentage(item["percentage"])
        )

    console.print(type_table)


@app.command(name="claims-by-status")
def claims_by_status_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Claims by Status report.

    Shows distribution of claims across different statuses (open/closed).
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_CLAIMS_BY_STATUS_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display claims by status data
    status_table = Table(title="Claims by Status")
    status_table.add_column("Status", style="cyan")
    status_table.add_column("Count", style="green", justify="right")
    status_table.add_column("Percentage", style="yellow", justify="right")

    for item in report["data"]:
        # Use appropriate color based on status
        status_style = "green" if item["status"] == "Closed" else "red"

        status_table.add_row(
            f"[{status_style}]{item['status']}[/{status_style}]",
            str(item["count"]),
            format_percentage(item["percentage"]),
        )

    console.print(status_table)


@app.command(name="claims-over-time")
def claims_over_time_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Claims Over Time report.

    Shows trend of new claims over time.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_CLAIMS_OVER_TIME_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display claims over time data
    time_table = Table(title="Claims Over Time")
    time_table.add_column("Period", style="cyan")
    time_table.add_column("New Claims", style="green", justify="right")
    time_table.add_column("Closed Claims", style="blue", justify="right")

    for item in report["data"]:
        time_table.add_row(
            format_date(item["period_start"]),
            str(item["new_claims_count"]),
            str(item["closed_claims_count"]),
        )

    console.print(time_table)


@app.command(name="financial-kpis")
def financial_kpi_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Financial KPI report with key financial indicators.

    Shows reserves, payments, and average values.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_FINANCIAL_KPIS_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display financial KPI data
    data = report["data"]

    kpi_table = Table(title="Financial KPIs")
    kpi_table.add_column("Metric", style="cyan")
    kpi_table.add_column("Value", style="green")
    kpi_table.add_column("Change", style="yellow")

    kpi_table.add_row(
        "Total Reserves",
        format_decimal(data["total_reserves"]),
        _format_change(data.get("total_reserves_change")),
    )

    kpi_table.add_row(
        "Total Payments",
        format_decimal(data["total_payments"]),
        _format_change(data.get("total_payments_change")),
    )

    kpi_table.add_row(
        "Average Reserve per Claim",
        format_decimal(data["avg_claim_value"]),
        _format_change(data.get("avg_claim_value_change")),
    )

    # Get payment info from data
    avg_payment_field = (
        "avg_payment_per_claim" if "avg_payment_per_claim" in data else "recovery_amount"
    )
    avg_payment_change_field = (
        "avg_payment_per_claim_change"
        if "avg_payment_per_claim_change" in data
        else "recovery_amount_change"
    )

    kpi_table.add_row(
        "Average Payment per Claim" if "avg_payment_per_claim" in data else "Recovery Amount",
        format_decimal(data[avg_payment_field]),
        _format_change(data.get(avg_payment_change_field)),
    )

    console.print(kpi_table)


@app.command(name="payments-vs-reserves")
def payments_vs_reserves_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Payments vs Reserves report.

    Shows the relationship between payments and reserves over time.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_PAYMENTS_VS_RESERVES_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display payments vs reserves data
    time_table = Table(title="Payments vs Reserves Over Time")
    time_table.add_column("Period", style="cyan")
    time_table.add_column("Payments", style="green", justify="right")
    time_table.add_column("Reserves", style="yellow", justify="right")

    for item in report["data"]:
        time_table.add_row(
            format_date(item["period"]),
            format_decimal(item["payments"]),
            format_decimal(item["reserves"]),
        )

    console.print(time_table)


@app.command(name="adjuster-performance")
def adjuster_performance_report(
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get the Adjuster Performance report.

    Shows performance metrics for adjusters including resolution times and task management.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(REPORT_ADJUSTER_PERFORMANCE_PATH, params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    # Display adjuster performance data
    perf_table = Table(title="Adjuster Performance")
    perf_table.add_column("Adjuster", style="cyan")
    perf_table.add_column("Claims", justify="right")
    perf_table.add_column("Avg Resolution", justify="right")
    perf_table.add_column("Total Payments", justify="right")
    perf_table.add_column("Pending Tasks", justify="right")
    perf_table.add_column("Completed Tasks", justify="right")

    for adjuster in report["data"]:
        perf_table.add_row(
            adjuster["user_name"],
            str(adjuster["claims_handled"]),
            f"{adjuster['avg_resolution_time']:.1f} days",
            format_decimal(adjuster["total_payments"]),
            str(adjuster["pending_tasks"]),
            f"[green]{adjuster['completed_tasks']}[/green]",
        )

    console.print(perf_table)


@app.command(name="generic")
def generic_report(
    report_name: str = typer.Argument(..., help="Name of the report to generate"),
    period: Optional[str] = PERIOD_OPTION,
    start_date: Optional[str] = START_DATE_OPTION,
    end_date: Optional[str] = END_DATE_OPTION,
    compare_period: bool = COMPARE_PERIOD_OPTION,
    customer_id: Optional[UUID] = CUSTOMER_ID_OPTION,
    claim_type: Optional[str] = CLAIM_TYPE_OPTION,
    user_id: Optional[UUID] = USER_ID_OPTION,
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Generate a generic report by name.

    Use this command to generate any report by specifying its name.
    """
    params = _build_report_params(
        period, start_date, end_date, compare_period, customer_id, claim_type, user_id
    )

    response = api_client.get(f"{REPORTS_PATH}/{report_name}", params=params)
    report = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=report)
        return

    _display_report_metadata(report["report_metadata"])

    console.print(report)  # Just print raw response for generic reports


def _format_change(change_data: Optional[dict]) -> str:
    """Format metric change data for display."""
    if not change_data:
        return ""

    direction = change_data.get("direction", "neutral")
    percentage = change_data.get("percentage", 0)

    if direction == "increase":
        color = "green"
        arrow = "↑"
    elif direction == "decrease":
        color = "red"
        arrow = "↓"
    else:
        color = "yellow"
        arrow = "="

    return f"[{color}]{arrow} {percentage:.1f}%[/{color}]"


if __name__ == "__main__":
    app()
