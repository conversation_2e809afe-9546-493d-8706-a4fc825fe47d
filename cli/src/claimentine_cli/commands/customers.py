"""Customer CLI commands."""

import json
from typing import Optional
from uuid import UUID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CUSTOMERS_PATH

app = typer.Typer(help="Customer management commands")
console = Console()


@app.command()
def list(
    active_only: bool = typer.Option(False, help="Show only active customers"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List all customers."""
    params = {}
    if active_only:
        params["active_only"] = "true"

    response = api_client.get(CUSTOMERS_PATH, params=params)
    customers = response.json()

    if not customers:
        if output == "json":
            print(json.dumps([]))
        else:
            console.print("No customers found")
        return

    if output == "json":
        print(json.dumps(customers, indent=2))
        return

    table = Table(title="Customers")
    table.add_column("ID", style="dim")
    table.add_column("Name", style="green")
    table.add_column("Prefix", style="blue")
    table.add_column("Description", style="yellow")
    table.add_column("Status", style="cyan")

    for customer in customers:
        table.add_row(
            str(customer["id"]),
            customer["name"],
            customer["prefix"],
            customer.get("description", ""),
            "Active" if customer["active"] else "Inactive",
        )

    console.print(table)


@app.command()
def get(
    customer_id: UUID = typer.Argument(..., help="Customer ID"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Get customer details."""
    response = api_client.get(f"{CUSTOMERS_PATH}/{customer_id}")
    customer = response.json()

    if output == "json":
        console.print(json.dumps(customer, indent=2))
        return

    table = Table(title=f"Customer Details - {customer['name']}", show_header=False)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("ID", str(customer["id"]))
    table.add_row("Name", customer["name"])
    table.add_row("Prefix", customer["prefix"])
    table.add_row("Description", customer.get("description", ""))
    table.add_row("Status", "Active" if customer["active"] else "Inactive")
    table.add_row("Created At", customer["created_at"])
    table.add_row("Updated At", customer["updated_at"])

    console.print(table)


@app.command()
def create(
    name: str = typer.Option(..., help="Customer name"),
    prefix: str = typer.Option(..., help="Customer prefix (4 uppercase alphanumeric characters)"),
    description: Optional[str] = typer.Option(None, help="Customer description"),
    output: str = typer.Option("text", help="Output format: text or json"),
) -> None:
    """Create a new customer."""
    data = {
        "name": name,
        "prefix": prefix,
        "description": description,
    }

    response = api_client.post(CUSTOMERS_PATH, json=data)
    customer = response.json()

    if output == "json":
        print(json.dumps(customer, indent=2))
        return

    console.print("Customer created successfully!", style="green")
    console.print(f"Name: {customer['name']}")
    console.print(f"Prefix: {customer['prefix']}")
    console.print(f"ID: {customer['id']}")


@app.command()
def update(
    customer_id: UUID = typer.Argument(..., help="Customer ID"),
    name: Optional[str] = typer.Option(None, help="Updated customer name"),
    description: Optional[str] = typer.Option(None, help="Updated customer description"),
    active: Optional[bool] = typer.Option(
        None, "--active/--no-active", help="Update active status"
    ),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Update a customer's details."""
    # Prepare update data
    data = {}
    if name is not None:
        data["name"] = name
    if description is not None:
        data["description"] = description
    if active is not None:
        data["active"] = active

    if not data:
        console.print("No update fields provided", style="yellow")
        raise typer.Exit(1)

    try:
        # Call the API to update the customer
        response = api_client.patch(f"{CUSTOMERS_PATH}/{customer_id}", json=data)
        customer = response.json()

        if output == "json":
            console.print(json.dumps(customer, indent=2))
            return

        console.print("Customer updated successfully!", style="green")

        # Display the updated customer details
        table = Table(title=f"Updated Customer - {customer['name']}", show_header=False)
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("ID", str(customer["id"]))
        table.add_row("Name", customer["name"])
        table.add_row("Prefix", customer["prefix"])
        table.add_row("Description", customer.get("description", ""))
        table.add_row("Status", "Active" if customer["active"] else "Inactive")
        table.add_row("Created At", customer["created_at"])
        table.add_row("Updated At", customer["updated_at"])

        console.print(table)

    except Exception as e:
        console.print(f"Error updating customer: {e}", style="red")
        raise typer.Exit(1)


@app.command("delete")
def delete_customer(
    customer_id: UUID = typer.Argument(..., help="ID of the customer to delete"),
    output: str = typer.Option("text", help="Output format: text or json"),
) -> None:
    """Delete a customer (soft delete)."""
    try:
        api_client.delete(f"{CUSTOMERS_PATH}/{customer_id}")
        # delete returns 204 No Content, so response.json() will fail
        # We just check the status code was success (2xx)

        if output == "json":
            # Indicate success with a simple JSON message
            print(json.dumps({"message": "Customer deleted successfully", "id": str(customer_id)}))
        else:
            console.print(f"Customer {customer_id} deleted successfully.", style="green")

    except Exception as e:
        # Handle potential errors (e.g., 404 Not Found, 403 Forbidden)
        if output == "json":
            print(json.dumps({"error": str(e), "id": str(customer_id)}))
        else:
            console.print(f"Error deleting customer {customer_id}: {e}", style="red")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
