#!/usr/bin/env python3
import json
import re
from datetime import datetime

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


# --- Fixture for Creating an FNOL ---
@pytest.fixture(scope="function")
def fnol(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, customer: str) -> dict:
    """Creates a unique FNOL for the given customer WITH a policy number and returns its details."""
    today = datetime.now().strftime("%Y-%m-%d")
    timestamp = datetime.now().strftime("%H%M%S%f")
    fnol_description = f"FNOL via fixture {timestamp} for customer {customer}"
    policy_number_value = f"FIXPOL-{timestamp}"
    reporter_phone_value = f"555-111-{timestamp[:4]}"
    reporter_email_value = f"fixture.reporter.{timestamp[:8]}@example.com"
    fnol_id = None

    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--description",
        fnol_description,
        "--incident-date",
        today,
        "--incident-location",
        "Fixture FNOL Location",
        "--reported-by",
        "Fixture Reporter",
        "--policy-number",
        policy_number_value,
        "--incident-time",
        "14:30",
        "--incident-state",
        "CA",
        "--reporter-relationship",
        "INSURED",
        "--communication-preference",
        "EMAIL",
        "--reporter-phone",
        reporter_phone_value,
        "--reporter-email",
        reporter_email_value,
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_args, catch_exceptions=False)

    if create_result.exit_code != 0:
        pytest.fail(f"Failed to create FNOL for customer {customer} in fixture", pytrace=False)

    try:
        create_data = validate_json_response(create_result.stdout)
        created_fnol_number = create_data["fnol_number"]
    except (json.JSONDecodeError, ValueError, KeyError, AssertionError) as e:
        pytest.fail(
            f"Failed to parse/validate FNOL from create response in fixture: {e}", pytrace=False
        )

    get_result = runner.invoke(
        app, ["fnols", "get", created_fnol_number, "--output", "json"], catch_exceptions=False
    )

    if get_result.exit_code != 0:
        pytest.fail(
            f"Failed to get FNOL {created_fnol_number} after creation in fixture", pytrace=False
        )

    try:
        fnol_data = validate_json_response(get_result.stdout)
        fnol_id = fnol_data.get("id")
        assert fnol_id, "FNOL ID could not be determined from get response in fixture"
        assert (
            fnol_data.get("policy_number") == policy_number_value
        ), f"Policy number mismatch in fixture. Expected {policy_number_value}, got {fnol_data.get('policy_number')}"

    except (json.JSONDecodeError, ValueError, KeyError, AssertionError) as e:
        pytest.fail(
            f"Failed to parse/validate FNOL from get response in fixture: {e}", pytrace=False
        )

    fnol_info = {
        "id": fnol_id,
        "policy_number": policy_number_value,
        "number": created_fnol_number,
        "description": fnol_description,
        "reporter_phone": reporter_phone_value,
        "reporter_email": reporter_email_value,
    }
    yield fnol_info

    # Try to delete the FNOL, but don't fail if it can't be deleted
    # (might have been converted to a claim already)
    try:
        delete_result = runner.invoke(
            app,
            ["fnols", "delete", fnol_id, "--use-id", "--yes"],
            catch_exceptions=False,
        )
    except Exception:
        # Just ignore errors during cleanup
        pass


# --- Test Functions ---


# Add new test for FNOL enhancements
def test_fnol_create_with_new_fields(runner: CliRunner, customer: str):
    """Test creating an FNOL with the new fields AND a policy number."""
    test_policy_num = f"NEWFIELDSPOL-{datetime.now().strftime('%H%M%S%f')}"
    test_reporter_phone = "************"
    test_reporter_email = "<EMAIL>"

    # Create FNOL with all the new fields and a policy number using JSON output
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "New Fields Reporter",
        "--description",
        "FNOL with new fields and policy test",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-time",
        "15:45",
        "--incident-state",
        "TX",
        "--reporter-relationship",
        "ATTORNEY",
        "--communication-preference",
        "PHONE",
        "--policy-number",
        test_policy_num,
        "--reporter-phone",
        test_reporter_phone,
        "--reporter-email",
        test_reporter_email,
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args)
    assert exit_code == 0

    # Parse the JSON response
    create_data = validate_json_response(stdout)

    # Assert on actual JSON data
    assert create_data["reported_by"] == "New Fields Reporter"
    assert create_data["policy_number"] == test_policy_num
    assert create_data["incident_state"] == "TX"
    assert create_data["reporter_relationship"] == "ATTORNEY"
    assert create_data["communication_preference"] == "PHONE"
    assert create_data["reporter_phone"] == test_reporter_phone
    assert create_data["reporter_email"] == test_reporter_email

    # Get the FNOL number from the JSON response
    fnol_number = create_data["fnol_number"]

    # Get FNOL details using JSON output
    get_exit_code, get_stdout = run_command(
        runner, ["fnols", "get", fnol_number, "--output", "json"]
    )
    assert get_exit_code == 0

    # Parse the JSON response
    get_data = validate_json_response(get_stdout)

    # Assert on the JSON data
    assert get_data["reported_by"] == "New Fields Reporter"
    assert get_data["policy_number"] == test_policy_num
    assert get_data["incident_state"] == "TX"
    assert get_data["reporter_relationship"] == "ATTORNEY"
    assert get_data["communication_preference"] == "PHONE"
    assert get_data["reporter_phone"] == test_reporter_phone
    assert get_data["reporter_email"] == test_reporter_email
    assert get_data["incident_time"] is not None
    assert "15:45" in get_data["incident_time"]

    # Test updating the reporter contact using JSON output
    updated_phone = "************"
    updated_email = "<EMAIL>"
    update_exit_code, update_stdout = run_command(
        runner,
        [
            "fnols",
            "update",
            fnol_number,
            "--reporter-phone",
            updated_phone,
            "--reporter-email",
            updated_email,
            "--output",
            "json",
        ],
    )
    assert update_exit_code == 0

    # Parse the JSON response
    update_data = validate_json_response(update_stdout)

    # Assert on the JSON data
    assert update_data["reporter_phone"] == updated_phone
    assert update_data["reporter_email"] == updated_email

    # Verify update with get using JSON output
    verify_exit_code, verify_stdout = run_command(
        runner, ["fnols", "get", fnol_number, "--output", "json"]
    )
    assert verify_exit_code == 0

    # Parse the JSON response
    verify_data = validate_json_response(verify_stdout)

    # Assert that the update was applied
    assert verify_data["reporter_phone"] == updated_phone
    assert verify_data["reporter_email"] == updated_email

    # Cleanup - delete the FNOL using JSON output
    delete_exit_code, delete_stdout = run_command(
        runner, ["fnols", "delete", fnol_number, "-y", "--output", "json"]
    )
    assert delete_exit_code == 0

    # For deletion, expect empty object response
    delete_data = validate_json_response(delete_stdout)
    assert delete_data == {}


def test_fnols_list(runner: CliRunner, customer: str, fnol: dict):
    """Test listing FNOLs with filtering by customer ID."""
    fnol_id = fnol["id"]
    fnol_policy_number = fnol["policy_number"]

    # Case 1: List all FNOLs
    exit_code, stdout = run_command(
        runner, ["fnols", "list", "--output", "json", "--page-size", "100"]
    )
    assert exit_code == 0
    all_fnols = validate_json_response(stdout)
    assert isinstance(all_fnols, list), "Expected a list of FNOLs"
    assert len(all_fnols) > 0, "Expected at least one FNOL in the system"
    # Verify the specific FNOL created by the fixture is present with correct policy number
    found_fixture_fnol = any(
        f["id"] == fnol_id and f.get("policy_number") == fnol_policy_number for f in all_fnols
    )
    assert found_fixture_fnol, f"FNOL {fnol_id} with policy {fnol_policy_number} not found in list"

    # Case 2: Filter by customer ID
    exit_code, stdout = run_command(
        runner, ["fnols", "list", "--customer-id", customer, "--output", "json"]
    )
    assert exit_code == 0
    customer_fnols = validate_json_response(stdout)
    assert isinstance(customer_fnols, list), "Expected a list of FNOLs for customer"
    assert len(customer_fnols) > 0, f"Expected at least one FNOL for customer {customer}"
    found_fixture_fnol_in_customer_list = any(
        f["id"] == fnol_id and f.get("policy_number") == fnol_policy_number for f in customer_fnols
    )
    assert (
        found_fixture_fnol_in_customer_list
    ), f"FNOL {fnol_id} with policy {fnol_policy_number} not found in filtered list for customer {customer}"
    for f_item in customer_fnols:
        assert (
            f_item["customer"]["id"] == customer
        ), f"FNOL {f_item['id']} has incorrect customer ID {f_item['customer']['id']}"

    # Case 3: Filter by non-existent customer ID
    non_existent_customer = "00000000-0000-0000-0000-000000000000"
    exit_code, stdout = run_command(
        runner,
        [
            "fnols",
            "list",
            "--customer-id",
            non_existent_customer,
            "--output",
            "json",
        ],
    )
    assert exit_code == 0
    no_customer_fnols = validate_json_response(stdout)
    assert isinstance(no_customer_fnols, list), "Expected a list for non-existent customer"
    assert (
        len(no_customer_fnols) == 0
    ), f"Expected no FNOLs for non-existent customer {non_existent_customer}"


def test_08_fnol_get(runner: CliRunner, fnol: dict):
    """Test retrieving a specific FNOL."""
    fnol_id = fnol["id"]
    fnol_policy_number = fnol["policy_number"]

    # Test output format: table
    exit_code, stdout = run_command(runner, ["fnols", "get", fnol_id, "--use-id"])
    assert exit_code == 0
    assert "FNOL Details" in stdout
    assert (
        fnol_policy_number in stdout
    ), f"Policy number {fnol_policy_number} not found in table output"

    # Test output format: json
    exit_code, stdout = run_command(
        runner, ["fnols", "get", fnol_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0
    try:
        fnol_data = validate_json_response(stdout)
        assert fnol_data["id"] == fnol_id, f"Expected FNOL ID {fnol_id}, got {fnol_data['id']}"
        assert (
            fnol_data["policy_number"] == fnol_policy_number
        ), f"Expected policy number {fnol_policy_number}, got {fnol_data['policy_number']}"
    except (json.JSONDecodeError, ValueError, KeyError, AssertionError) as e:
        pytest.fail(f"Failed to validate JSON response from get: {e}", pytrace=False)


def test_09_fnol_update(runner: CliRunner, fnol: dict):
    """Test updating a FNOL."""
    fnol_id = fnol["id"]
    fnol_number = fnol["number"]
    new_reporter = "Updated Reporter"
    new_policy_number = f"UPDATED-POL-{datetime.now().strftime('%H%M%S%f')}"

    update_exit_code, update_stdout = run_command(
        runner,
        [
            "fnols",
            "update",
            fnol_id,
            "--use-id",
            "--reported-by",
            new_reporter,
            "--policy-number",
            new_policy_number,
            "--reporter-relationship",
            "AGENT",
            "--communication-preference",
            "MAIL",
        ],
    )

    assert update_exit_code == 0
    assert "FNOL updated successfully!" in update_stdout
    assert new_reporter in update_stdout
    assert new_policy_number in update_stdout

    # Verify update with get
    get_exit_code, get_stdout = run_command(
        runner, ["fnols", "get", fnol_id, "--use-id", "--output", "json"]
    )
    assert get_exit_code == 0
    try:
        update_data = validate_json_response(get_stdout)
        assert update_data["id"] == fnol_id, f"Expected FNOL ID {fnol_id}, got {update_data['id']}"
        assert (
            update_data["reported_by"] == new_reporter
        ), f"Expected reported_by '{new_reporter}', got '{update_data['reported_by']}'"
        assert (
            update_data["policy_number"] == new_policy_number
        ), f"Expected policy_number '{new_policy_number}', got '{update_data['policy_number']}'"
        assert (
            update_data["reporter_relationship"] == "AGENT"
        ), f"Expected reporter_relationship 'AGENT', got '{update_data['reporter_relationship']}'"
        assert (
            update_data["communication_preference"] == "MAIL"
        ), f"Expected communication_preference 'MAIL', got '{update_data['communication_preference']}'"
    except (json.JSONDecodeError, ValueError, KeyError, AssertionError) as e:
        pytest.fail(f"Failed to validate JSON response after update: {e}", pytrace=False)


def test_fnol_delete(runner: CliRunner, customer: str):
    """Test deleting a FNOL."""
    # Create a FNOL specifically for this test
    timestamp = datetime.now().strftime("%H%M%S%f")
    fnol_desc = f"FNOL for deletion test {timestamp}"

    create_exit_code, create_stdout = run_command(
        runner,
        [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            "Test Deleter",
            "--description",
            fnol_desc,
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-state",
            "CA",
            "--reporter-phone",
            "************",
        ],
    )
    assert create_exit_code == 0, f"Failed to create FNOL for deletion test: {create_stdout}"

    # Extract FNOL number from creation output (simple regex approach)
    match = re.search(r"FNOL Number: (\S+)", create_stdout)
    assert match, f"Could not extract FNOL number from create output: {create_stdout}"
    fnol_number = match.group(1)

    # Get the FNOL ID using the number
    get_exit_code, get_stdout = run_command(
        runner, ["fnols", "get", fnol_number, "--output", "json"]
    )
    assert get_exit_code == 0, f"Failed to get created FNOL details: {get_stdout}"
    fnol_data = validate_json_response(get_stdout)
    fnol_id = fnol_data["id"]

    # --- Test 1: Attempt delete, confirm NO ---
    result = runner.invoke(app, ["fnols", "delete", fnol_id, "--use-id"], input="n\n")
    assert isinstance(
        result.exception, SystemExit
    ), "Expected SystemExit exception when confirming no"
    assert result.exit_code != 0, "Expected non-zero exit code for aborted confirmation"

    # Verify FNOL still exists
    get_exit_code, get_stdout = run_command(runner, ["fnols", "get", fnol_id, "--use-id"])
    assert get_exit_code == 0, f"FNOL {fnol_id} should still exist after aborted delete."

    # --- Test 2: Attempt delete, confirm YES ---
    delete_exit_code, delete_stdout = run_command(
        runner,
        ["fnols", "delete", fnol_id, "--use-id"],
        input_str="y\n",
    )
    assert delete_exit_code == 0, f"Delete failed when confirming yes: {delete_stdout}"
    assert (
        f"Successfully deleted FNOL {fnol_number}" in delete_stdout
    ), "Expected success message in output"

    # Verify FNOL is gone (expect 404)
    get_exit_code, get_stdout = run_command(
        runner, ["fnols", "get", fnol_id, "--use-id"], allow_error=True
    )
    assert (
        get_exit_code == 404
    ), f"Expected 404 when getting deleted FNOL {fnol_id}, got {get_exit_code}"

    # --- Test 3: Delete using --yes flag ---
    # Create another FNOL
    fnol_desc_2 = f"FNOL for --yes deletion test {datetime.now().isoformat()}"
    create_exit_code_2, create_stdout_2 = run_command(
        runner,
        [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            "Test Yes Deleter",
            "--description",
            fnol_desc_2,
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-state",
            "TX",
            "--reporter-phone",
            "************",
        ],
    )
    assert create_exit_code_2 == 0, f"Failed to create second FNOL: {create_stdout_2}"
    match_2 = re.search(r"FNOL Number: (\S+)", create_stdout_2)
    assert match_2, f"Could not extract FNOL number from second create output: {create_stdout_2}"
    fnol_number_2 = match_2.group(1)
    get_exit_code_2, get_stdout_2 = run_command(
        runner, ["fnols", "get", fnol_number_2, "--output", "json"]
    )
    assert get_exit_code_2 == 0, f"Failed to get second FNOL details: {get_stdout_2}"
    fnol_data_2 = validate_json_response(get_stdout_2)
    fnol_id_2 = fnol_data_2["id"]

    # Delete with --yes
    delete_exit_code_2, delete_stdout_2 = run_command(
        runner, ["fnols", "delete", fnol_id_2, "--use-id", "--yes"]
    )
    assert delete_exit_code_2 == 0, f"Delete with --yes failed: {delete_stdout_2}"
    assert (
        f"Successfully deleted FNOL {fnol_number_2}" in delete_stdout_2
    ), "Expected success message with --yes"

    # Verify FNOL 2 is gone
    get_exit_code_2, get_stdout_2 = run_command(
        runner, ["fnols", "get", fnol_id_2, "--use-id"], allow_error=True
    )
    assert (
        get_exit_code_2 == 404
    ), f"Expected 404 for second deleted FNOL {fnol_id_2}, got {get_exit_code_2}"

    # --- Test 4: Delete non-existent FNOL ---
    delete_exit_code_3, delete_stdout_3 = run_command(
        runner, ["fnols", "delete", fnol_id_2, "--use-id", "--yes"], allow_error=True
    )
    assert (
        delete_exit_code_3 == 404
    ), f"Expected 404 when deleting non-existent FNOL {fnol_id_2}, got {delete_exit_code_3}"


def test_fnol_convert(runner: CliRunner, fnol: dict):
    """Test converting an FNOL to a claim."""
    fnol_id = fnol["id"]

    # Convert FNOL to AUTO claim using JSON output
    exit_code, stdout = run_command(
        runner,
        [
            "fnols",
            "convert",
            fnol_id,
            "--claim-type",
            "AUTO",
            "--use-id",
            "--output",
            "json",
        ],
    )
    assert exit_code == 0

    # Parse the JSON response
    claim_data = validate_json_response(stdout)

    # Assert on the claim data
    assert "claim_id" in claim_data
    assert "claim_number" in claim_data
    assert claim_data["claim_type"] == "AUTO"
    assert "status" in claim_data


# NEW VALIDATION TESTS FOR ELY-1008 ENHANCEMENTS


def test_fnol_contact_validation_phone_only(runner: CliRunner, customer: str):
    """Test creating FNOL with only phone contact (should succeed)."""
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "Phone Only Reporter",
        "--description",
        "Test with phone only",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "CA",
        "--reporter-phone",
        "************",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args)
    assert exit_code == 0

    # Parse and verify
    data = validate_json_response(stdout)
    assert data["reporter_phone"] == "************"
    assert data["reporter_email"] is None

    # Cleanup
    run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_contact_validation_email_only(runner: CliRunner, customer: str):
    """Test creating FNOL with only email contact (should succeed)."""
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "Email Only Reporter",
        "--description",
        "Test with email only",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "FL",
        "--reporter-email",
        "<EMAIL>",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args)
    assert exit_code == 0

    # Parse and verify
    data = validate_json_response(stdout)
    assert data["reporter_email"] == "<EMAIL>"
    assert data["reporter_phone"] is None

    # Cleanup
    run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_contact_validation_no_contact_fails(runner: CliRunner, customer: str):
    """Test creating FNOL with no contact methods fails."""
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "No Contact Reporter",
        "--description",
        "Test with no contact",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "NY",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args, allow_error=True)
    assert exit_code == 1  # Should fail
    # No JSON to parse because it should fail at validation level


def test_fnol_incident_state_validation(runner: CliRunner, customer: str):
    """Test incident_state field accepts valid USState enum values."""
    test_states = ["AL", "CA", "TX", "NY", "UNKNOWN"]

    for state in test_states:
        create_args = [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            f"Reporter for {state}",
            "--description",
            f"Test incident in {state}",
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-state",
            state,
            "--reporter-phone",
            "************",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, create_args)
        assert exit_code == 0, f"Failed to create FNOL with state {state}"

        # Parse and verify
        data = validate_json_response(stdout)
        assert data["incident_state"] == state

        # Cleanup
        run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_reporter_relationship_validation(runner: CliRunner, customer: str):
    """Test reporter_relationship field accepts valid enum values."""
    test_relationships = ["INSURED", "CLAIMANT", "ATTORNEY", "AGENT", "OTHER"]

    for relationship in test_relationships:
        create_args = [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            f"Reporter {relationship}",
            "--description",
            f"Test with relationship {relationship}",
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-state",
            "TX",
            "--reporter-relationship",
            relationship,
            "--reporter-email",
            f"{relationship.lower()}@example.com",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, create_args)
        assert exit_code == 0, f"Failed to create FNOL with relationship {relationship}"

        # Parse and verify
        data = validate_json_response(stdout)
        assert data["reporter_relationship"] == relationship

        # Cleanup
        run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_time_format_validation(runner: CliRunner, customer: str):
    """Test incident_time field accepts valid time formats."""
    valid_times = ["09:30", "15:45", "00:00", "23:59"]

    for time_str in valid_times:
        create_args = [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            f"Time Test Reporter",
            "--description",
            f"Test with time {time_str}",
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-time",
            time_str,
            "--incident-state",
            "IL",
            "--reporter-phone",
            "************",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, create_args)
        assert exit_code == 0, f"Failed to create FNOL with time {time_str}"

        # Parse and verify
        data = validate_json_response(stdout)
        assert data["incident_time"] is not None
        assert time_str in data["incident_time"]

        # Cleanup
        run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_time_format_validation_invalid(runner: CliRunner, customer: str):
    """Test incident_time field rejects invalid time formats."""
    invalid_times = ["25:00", "12:60", "not-a-time", "1:30 PM"]

    for time_str in invalid_times:
        create_args = [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            f"Invalid Time Test Reporter",
            "--description",
            f"Test with invalid time {time_str}",
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-time",
            time_str,
            "--incident-state",
            "OH",
            "--reporter-phone",
            "************",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, create_args, allow_error=True)
        assert exit_code == 1, f"Invalid time {time_str} should have failed validation"


def test_fnol_update_contact_validation(runner: CliRunner, customer: str):
    """Test updating FNOL contact fields maintains validation."""
    # Create FNOL with both contacts
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "Update Test Reporter",
        "--description",
        "Test contact update validation",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "MI",
        "--reporter-phone",
        "************",
        "--reporter-email",
        "<EMAIL>",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args)
    assert exit_code == 0

    data = validate_json_response(stdout)
    fnol_number = data["fnol_number"]

    # Test updating to phone only
    update_args = [
        "fnols",
        "update",
        fnol_number,
        "--reporter-phone",
        "************",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, update_args)
    assert exit_code == 0

    data = validate_json_response(stdout)
    assert data["reporter_phone"] == "************"

    # Test updating to email only
    update_args = [
        "fnols",
        "update",
        fnol_number,
        "--reporter-email",
        "<EMAIL>",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, update_args)
    assert exit_code == 0

    data = validate_json_response(stdout)
    assert data["reporter_email"] == "<EMAIL>"

    # Cleanup
    run_command(runner, ["fnols", "delete", fnol_number, "-y", "--output", "json"])


def test_fnol_communication_preference_validation(runner: CliRunner, customer: str):
    """Test communication_preference field accepts valid enum values."""
    valid_preferences = ["EMAIL", "PHONE", "TEXT", "MAIL", "PORTAL", "NO_PREFERENCE"]

    for preference in valid_preferences:
        create_args = [
            "fnols",
            "create",
            "--customer-id",
            customer,
            "--reported-by",
            f"Comm Pref {preference} Reporter",
            "--description",
            f"Test with communication preference {preference}",
            "--incident-date",
            datetime.now().strftime("%Y-%m-%d"),
            "--incident-state",
            "OR",
            "--communication-preference",
            preference,
            "--reporter-phone",
            "************",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, create_args)
        assert exit_code == 0, f"Failed to create FNOL with communication preference {preference}"

        # Parse and verify
        data = validate_json_response(stdout)
        assert data["communication_preference"] == preference

        # Cleanup
        run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])


def test_fnol_policy_number_optional(runner: CliRunner, customer: str):
    """Test that policy_number field is optional."""
    # Test without policy number
    create_args = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "No Policy Reporter",
        "--description",
        "Test without policy number",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "WA",
        "--reporter-email",
        "<EMAIL>",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args)
    assert exit_code == 0

    data = validate_json_response(stdout)
    assert data["policy_number"] is None

    # Cleanup
    run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])

    # Test with policy number
    create_args_with_policy = [
        "fnols",
        "create",
        "--customer-id",
        customer,
        "--reported-by",
        "Policy Reporter",
        "--description",
        "Test with policy number",
        "--incident-date",
        datetime.now().strftime("%Y-%m-%d"),
        "--incident-state",
        "WA",
        "--reporter-email",
        "<EMAIL>",
        "--policy-number",
        "POL-12345-67890",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, create_args_with_policy)
    assert exit_code == 0

    data = validate_json_response(stdout)
    assert data["policy_number"] == "POL-12345-67890"

    # Cleanup
    run_command(runner, ["fnols", "delete", data["fnol_number"], "-y", "--output", "json"])
