#!/usr/bin/env python3
import json
import re
from typing import Dict, Optional

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app


def validate_json_response(stdout: str) -> Dict:
    """Helper function to validate and parse JSON response."""
    # Remove any ANSI color codes
    ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
    stdout = ansi_escape.sub("", stdout)

    # Remove any debug lines
    if "DEBUG:" in stdout:
        cleaned_lines = []
        for line in stdout.splitlines():
            if not line.strip().startswith("DEBUG:"):
                cleaned_lines.append(line)
        stdout = "\n".join(cleaned_lines)

    try:
        return json.loads(stdout)
    except json.JSONDecodeError:
        raise AssertionError(f"Invalid JSON response: {stdout}")


def test_reports_claims_kpis(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    """Test the claims KPIs report command."""
    print("\nRunning test_reports_claims_kpis...")

    # Basic claims KPIs report command
    result = runner.invoke(app, ["reports", "claims-kpis"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title
    assert "Claims KPIs" in result.stdout
    assert "Report: claims_kpis" in result.stdout
    assert "Generated at:" in result.stdout

    print("✓ test_reports_claims_kpis passed")


def test_reports_claims_by_type(runner: CliRunner):
    """Test the claims by type report command."""
    print("\nRunning test_reports_claims_by_type...")

    # Claims by type report command
    result = runner.invoke(app, ["reports", "claims-by-type"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title and columns
    assert "Claims by Type" in result.stdout
    assert "Claim Type" in result.stdout
    assert "Count" in result.stdout
    assert "Percentage" in result.stdout

    print("✓ test_reports_claims_by_type passed")


def test_reports_claims_by_status(runner: CliRunner):
    """Test the claims by status report command."""
    print("\nRunning test_reports_claims_by_status...")

    # Claims by status report command
    result = runner.invoke(app, ["reports", "claims-by-status"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title and columns
    assert "Claims by Status" in result.stdout
    assert "Status" in result.stdout
    assert "Count" in result.stdout
    assert "Percentage" in result.stdout

    print("✓ test_reports_claims_by_status passed")


def test_reports_claims_over_time(runner: CliRunner):
    """Test the claims over time report command."""
    print("\nRunning test_reports_claims_over_time...")

    # Claims over time report command
    result = runner.invoke(app, ["reports", "claims-over-time"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title
    assert "Claims Over Time" in result.stdout

    print("✓ test_reports_claims_over_time passed")


def test_reports_financial_kpis(runner: CliRunner):
    """Test the financial KPIs report command."""
    print("\nRunning test_reports_financial_kpis...")

    # Financial KPIs report command
    result = runner.invoke(app, ["reports", "financial-kpis"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title and metrics
    assert "Financial KPIs" in result.stdout

    print("✓ test_reports_financial_kpis passed")


def test_reports_payments_vs_reserves(runner: CliRunner):
    """Test the payments vs reserves report command."""
    print("\nRunning test_reports_payments_vs_reserves...")

    # Payments vs reserves report command
    result = runner.invoke(app, ["reports", "payments-vs-reserves"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title
    assert "Payments vs Reserves" in result.stdout

    print("✓ test_reports_payments_vs_reserves passed")


def test_reports_adjuster_performance(runner: CliRunner):
    """Test the adjuster performance report command."""
    print("\nRunning test_reports_adjuster_performance...")

    # Adjuster performance report command
    result = runner.invoke(app, ["reports", "adjuster-performance"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected report title
    assert "Adjuster Performance" in result.stdout

    print("✓ test_reports_adjuster_performance passed")


def test_reports_with_filter_options(runner: CliRunner, customer: str):
    """Test reports with filter options."""
    print("\nRunning test_reports_with_filter_options...")

    # Claims KPIs report with filter options
    result = runner.invoke(
        app,
        [
            "reports",
            "claims-kpis",
            "--period",
            "last_6_months",
            "--customer-id",
            customer,
            "--compare-period",
        ],
        catch_exceptions=False,
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected filter info
    assert "Filters applied:" in result.stdout
    assert (
        "period: last_6_months" in result.stdout.lower() or "period: last_6_months" in result.stdout
    )
    assert customer in result.stdout

    print("✓ test_reports_with_filter_options passed")


def test_reports_with_custom_dates(runner: CliRunner):
    """Test reports with custom date ranges."""
    print("\nRunning test_reports_with_custom_dates...")

    # Claims KPIs report with custom date range
    result = runner.invoke(
        app,
        ["reports", "claims-kpis", "--start-date", "2023-01-01", "--end-date", "2023-12-31"],
        catch_exceptions=False,
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected filter info
    assert "Filters applied:" in result.stdout
    assert "2023-01-01" in result.stdout
    assert "2023-12-31" in result.stdout

    print("✓ test_reports_with_custom_dates passed")


def test_reports_json_output(runner: CliRunner):
    """Test reports with JSON output."""
    print("\nRunning test_reports_json_output...")

    # Claims KPIs report with JSON output
    result = runner.invoke(
        app, ["reports", "claims-kpis", "--output", "json"], catch_exceptions=True
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate JSON response
    try:
        json_data = validate_json_response(result.stdout)
        assert "report_metadata" in json_data
        assert "data" in json_data
        assert json_data["report_metadata"]["report_name"] == "claims_kpis"
    except Exception as e:
        pytest.fail(f"Failed to validate JSON response: {e}")

    print("✓ test_reports_json_output passed")
