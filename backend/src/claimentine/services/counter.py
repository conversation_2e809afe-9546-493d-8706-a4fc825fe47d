"""Service layer for managing counters."""

import logging

from sqlalchemy.future import select
from sqlalchemy.orm import Session

from claimentine.models.counter import CustomerTaskCounter
from claimentine.models.customer import Customer

logger = logging.getLogger(__name__)


def get_next_task_number(db: Session, customer: Customer) -> int:
    """Gets the next task sequence number for a customer atomically.

    Assumes the provided customer object is valid and exists in the session.
    Uses SELECT FOR UPDATE to lock the row, preventing race conditions.
    Creates the counter row if it doesn't exist.

    Args:
        db: The Session instance.
        customer: The Customer object.

    Returns:
        The next task number for the customer.

    Raises:
        Exception: For database errors during the transaction.
    """

    # Customer existence check is removed - assuming customer object is valid

    customer_id = customer.id  # Get ID from the object

    with db.begin_nested():  # Use nested transaction for FOR UPDATE
        # Lock the counter row for the customer
        stmt = select(CustomerTaskCounter).where(CustomerTaskCounter.customer_id == customer_id).with_for_update()
        result = db.execute(stmt)
        counter = result.scalar_one_or_none()

        if counter:
            # Increment the existing counter
            counter.last_task_number += 1
            next_number = counter.last_task_number
            logger.debug(f"Incremented task counter for customer {customer_id} to {next_number}")
        else:
            # Create a new counter for this customer
            next_number = 1
            counter = CustomerTaskCounter(customer_id=customer_id, last_task_number=next_number)
            db.add(counter)
            logger.info(f"Created new task counter for customer {customer_id}, starting at {next_number}")

        # The nested transaction commits here, releasing the lock.

    return next_number
