"""Customer service."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.customer import Customer
from claimentine.models.user import User
from claimentine.schemas.customer import CustomerCreate, CustomerUpdate
from claimentine.services.base import BaseService


class CustomerService(BaseService):
    """Service for managing customers."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_customer_by_id(self, customer_id: UUID) -> Optional[Customer]:
        """Get customer by ID."""
        stmt = select(Customer).where(Customer.id == customer_id, Customer.is_deleted == False)
        return self.db.scalar(stmt)

    def get_customer_by_prefix(self, prefix: str) -> Optional[Customer]:
        """Get customer by prefix."""
        stmt = select(Customer).where(Customer.prefix == prefix, Customer.is_deleted == False)
        return self.db.scalar(stmt)

    def list_customers(self, active_only: bool = False) -> List[Customer]:
        """List all customers."""
        # Check permission to view customers
        self.check_permission("VIEW_CUSTOMERS", "customer")

        stmt = select(Customer)
        if active_only:
            stmt = stmt.where(Customer.active)

        # Filter out soft-deleted records
        stmt = stmt.where(Customer.is_deleted == False)

        return list(self.db.scalars(stmt))

    def create_customer(self, data: CustomerCreate) -> Customer:
        """Create a new customer."""
        # Check permission to create customers
        self.check_permission("CREATE_CUSTOMERS", "customer")

        # Check if prefix already exists (and is not soft-deleted)
        if self.get_customer_by_prefix(data.prefix):
            raise DuplicateError(f"Customer with prefix {data.prefix} already exists")

        customer = Customer(**data.model_dump())
        self.db.add(customer)
        self.db.commit()
        self.db.refresh(customer)
        return customer

    def update_customer(self, customer_id: UUID, data: CustomerUpdate) -> Customer:
        """Update a customer."""
        # Check permission to edit customers
        self.check_permission("EDIT_CUSTOMERS", "customer", customer_id)

        customer = self.get_customer_by_id(customer_id)
        if not customer:
            raise NotFoundError(f"Customer {customer_id} not found")

        # Update fields
        update_data = data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(customer, key, value)

        self.db.commit()
        self.db.refresh(customer)
        return customer

    def delete_customer(self, customer_id: UUID) -> None:
        """Delete a customer.

        Note: This will fail if there are any claims associated with the customer
        due to the RESTRICT foreign key constraint.
        """
        # Check permission to delete customers
        self.check_permission("DELETE_CUSTOMERS", "customer", customer_id)

        customer = self.get_customer_by_id(customer_id)
        if not customer:
            raise NotFoundError(f"Customer {customer_id} not found")

        # Soft delete: Set flags
        customer.is_deleted = True
        customer.deleted_at = datetime.utcnow()
        customer.active = False
        self.db.commit()
