"""Service for claims-related reports."""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import UUID

from sqlalchemy import case, func, select
from sqlalchemy.orm import Session

from claimentine.models.claim.base import BaseClaim, ClaimStatus, ClaimType
from claimentine.models.user import User
from claimentine.schemas.reports.base import ReportRequest
from claimentine.schemas.reports.claims_by_status import ClaimsByStatusItem, ReportResponseClaimsByStatus
from claimentine.schemas.reports.claims_by_type import ClaimsByTypeItem, ReportResponseClaimsByType
from claimentine.schemas.reports.claims_kpi import ClaimsKpiData, ReportResponseClaimsKpis
from claimentine.schemas.reports.claims_over_time import ClaimsOverTimeItem, ReportResponseClaimsOverTime
from claimentine.services.reports.base import BaseReportService

logger = logging.getLogger(__name__)


class ClaimReportService(BaseReportService):
    """Service for claims-related reports."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_claims_kpi_report(self, request: ReportRequest) -> ReportResponseClaimsKpis:
        """Generate Claims KPI report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseClaimsKpis object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))

        # Define closed statuses condition
        closed_statuses = [ClaimStatus.CLOSED_SETTLED, ClaimStatus.CLOSED_DENIED, ClaimStatus.CLOSED_WITHDRAWN]
        is_closed_condition = BaseClaim.status.in_(closed_statuses)

        # Get total claims
        total_claims_stmt = select(func.count()).select_from(BaseClaim).where(*claim_filters)
        total_claims = self.db.scalar(total_claims_stmt) or 0

        # Get open claims
        open_claims_stmt = select(func.count()).select_from(BaseClaim).where(*claim_filters, ~is_closed_condition)
        open_claims = self.db.scalar(open_claims_stmt) or 0

        # Get closed claims
        closed_claims_stmt = select(func.count()).select_from(BaseClaim).where(*claim_filters, is_closed_condition)
        closed_claims = self.db.scalar(closed_claims_stmt) or 0

        # Calculate percentages
        open_claims_percentage = (open_claims / total_claims * 100) if total_claims > 0 else 0
        closed_claims_percentage = (closed_claims / total_claims * 100) if total_claims > 0 else 0

        # Calculate average resolution time
        avg_resolution_filters = [
            *claim_filters,
            is_closed_condition,
            BaseClaim.closed_at <= end_date,
        ]
        # Only add start_date filter if it's not None
        if start_date is not None:
            avg_resolution_filters.append(BaseClaim.closed_at >= start_date)

        avg_resolution_stmt = (
            select(func.avg(BaseClaim.closed_at - BaseClaim.created_at))
            .select_from(BaseClaim)
            .where(*avg_resolution_filters)
        )
        avg_resolution_result = self.db.scalar(avg_resolution_stmt)
        avg_resolution_days = avg_resolution_result.total_seconds() / (60 * 60 * 24) if avg_resolution_result else None

        # Calculate metrics change if requested
        total_claims_change = None
        avg_resolution_days_change = None

        if request.compare_period:
            # Calculate previous period
            period_duration = end_date - start_date
            prev_end_date = start_date - timedelta(microseconds=1)
            prev_start_date = prev_end_date - period_duration

            # Previous period total claims
            prev_total_claims_stmt = (
                select(func.count()).select_from(BaseClaim).where(*claim_filters, BaseClaim.created_at <= prev_end_date)
            )
            prev_total_claims = self.db.scalar(prev_total_claims_stmt) or 0

            if prev_total_claims > 0:
                total_claims_change = self._calculate_metric_change(total_claims, prev_total_claims)

            # Previous period resolution time
            prev_resolution_filters = [
                *claim_filters,
                is_closed_condition,
                BaseClaim.closed_at <= prev_end_date,
            ]
            # Only add prev_start_date filter if it's not None
            if prev_start_date is not None:
                prev_resolution_filters.append(BaseClaim.closed_at >= prev_start_date)

            prev_avg_resolution_stmt = (
                select(func.avg(BaseClaim.closed_at - BaseClaim.created_at))
                .select_from(BaseClaim)
                .where(*prev_resolution_filters)
            )
            prev_avg_resolution_result = self.db.scalar(prev_avg_resolution_stmt)
            prev_avg_resolution_days = (
                prev_avg_resolution_result.total_seconds() / (60 * 60 * 24) if prev_avg_resolution_result else None
            )

            if prev_avg_resolution_days and avg_resolution_days:
                avg_resolution_days_change = self._calculate_metric_change(
                    avg_resolution_days, prev_avg_resolution_days
                )

        # Create column headers
        column_headers = [
            {"key": "metric", "label": "Metric"},
            {"key": "value", "label": "Value"},
            {"key": "percentage", "label": "Percentage"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="claims_kpis", request=request, column_headers=column_headers
        )

        # Create report data
        data = ClaimsKpiData(
            total_claims=total_claims,
            total_claims_change=total_claims_change,
            open_claims=open_claims,
            open_claims_percentage=open_claims_percentage,
            closed_claims=closed_claims,
            closed_claims_percentage=closed_claims_percentage,
            avg_resolution_time_days=avg_resolution_days,
            avg_resolution_time_days_change=avg_resolution_days_change,
        )

        # Return report
        return ReportResponseClaimsKpis(report_metadata=metadata, data=data)

    def get_claims_by_type_report(self, request: ReportRequest) -> ReportResponseClaimsByType:
        """Generate Claims by Type report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseClaimsByType object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))
        if start_date:
            claim_filters.append(BaseClaim.created_at >= start_date)
        if end_date:
            claim_filters.append(BaseClaim.created_at <= end_date)

        # Query claims by type
        claims_by_type_stmt = (
            select(BaseClaim.type, func.count().label("count")).where(*claim_filters).group_by(BaseClaim.type)
        )

        claims_by_type_result = self.db.execute(claims_by_type_stmt).all()

        # Calculate total claims for percentage
        total_claims = sum(result.count for result in claims_by_type_result)

        # Define colors for chart consistency
        type_colors = {
            ClaimType.AUTO: "#4CAF50",
            ClaimType.PROPERTY: "#2196F3",
            ClaimType.GENERAL_LIABILITY: "#FFC107",
        }

        # Create list of items
        claims_by_type_items = []
        for result in claims_by_type_result:
            percentage = (result.count / total_claims * 100) if total_claims > 0 else 0
            claims_by_type_items.append(
                ClaimsByTypeItem(
                    claim_type=result.type.value,
                    count=result.count,
                    percentage=percentage,
                    color=type_colors.get(result.type, "#9E9E9E"),
                )
            )

        # Sort by count descending
        claims_by_type_items.sort(key=lambda x: x.count, reverse=True)

        # Create column headers
        column_headers = [
            {"key": "claim_type", "label": "Claim Type"},
            {"key": "count", "label": "Count"},
            {"key": "percentage", "label": "Percentage"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="claims_by_type", request=request, column_headers=column_headers
        )

        # Return report
        return ReportResponseClaimsByType(report_metadata=metadata, data=claims_by_type_items)

    def get_claims_by_status_report(self, request: ReportRequest) -> ReportResponseClaimsByStatus:
        """Generate Claims by Status report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseClaimsByStatus object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))
        if start_date:
            claim_filters.append(BaseClaim.created_at >= start_date)
        if end_date:
            claim_filters.append(BaseClaim.created_at <= end_date)

        # Query claims by status
        claims_by_status_stmt = (
            select(BaseClaim.status, func.count().label("count")).where(*claim_filters).group_by(BaseClaim.status)
        )

        claims_by_status_result = self.db.execute(claims_by_status_stmt).all()

        # Calculate total claims for percentage
        total_claims = sum(result.count for result in claims_by_status_result)

        # Define colors for chart consistency
        status_colors = {
            ClaimStatus.CLOSED_SETTLED: "#4CAF50",  # Closed claims - green
            ClaimStatus.CLOSED_DENIED: "#43A047",  # Closed denied - darker green
            ClaimStatus.CLOSED_WITHDRAWN: "#2E7D32",  # Closed withdrawn - darkest green
            ClaimStatus.DRAFT: "#FFC107",  # Draft claims - yellow
            ClaimStatus.INVESTIGATION: "#F44336",  # Investigation - red
            ClaimStatus.SETTLEMENT: "#E91E63",  # Settlement - pink
            ClaimStatus.LITIGATION: "#9C27B0",  # Litigation - purple
            ClaimStatus.RECOVERY: "#FF9800",  # Recovery - orange
        }

        # Create list of items
        claims_by_status_items = []
        for result in claims_by_status_result:
            percentage = (result.count / total_claims * 100) if total_claims > 0 else 0
            claims_by_status_items.append(
                ClaimsByStatusItem(
                    status=result.status.value,
                    count=result.count,
                    percentage=percentage,
                    color=status_colors.get(result.status, "#9E9E9E"),
                )
            )

        # Sort by count descending
        claims_by_status_items.sort(key=lambda x: x.count, reverse=True)

        # Create column headers
        column_headers = [
            {"key": "status", "label": "Status"},
            {"key": "count", "label": "Count"},
            {"key": "percentage", "label": "Percentage"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="claims_by_status", request=request, column_headers=column_headers
        )

        # Return report
        return ReportResponseClaimsByStatus(report_metadata=metadata, data=claims_by_status_items)

    def get_claims_over_time_report(self, request: ReportRequest) -> ReportResponseClaimsOverTime:
        """Generate Claims Over Time report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseClaimsOverTime object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))

        # Determine time interval based on date range
        interval = "month"
        if start_date is not None and (end_date - start_date).days <= 60:  # If range is <= 60 days, use weekly
            interval = "week"

        # Calculate periods
        periods = []
        if start_date is None:
            # If no start_date, we can't calculate meaningful periods, return empty data
            result_items = []
        elif interval == "month":
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                next_month = current_date.month + 1
                next_year = current_date.year
                if next_month > 12:
                    next_month = 1
                    next_year += 1

                period_end = datetime(next_year, next_month, 1) - timedelta(microseconds=1)
                periods.append((current_date, period_end))

                current_date = datetime(next_year, next_month, 1)

            # Query data for each period
            result_items = []
            for period_start, period_end in periods:
                # New claims in period
                new_claims_stmt = (
                    select(func.count())
                    .select_from(BaseClaim)
                    .where(*claim_filters, BaseClaim.created_at >= period_start, BaseClaim.created_at <= period_end)
                )
                new_claims_count = self.db.scalar(new_claims_stmt) or 0

                # Closed claims in period
                closed_claims_stmt = (
                    select(func.count())
                    .select_from(BaseClaim)
                    .where(
                        *claim_filters,
                        BaseClaim.status.in_(
                            [ClaimStatus.CLOSED_SETTLED, ClaimStatus.CLOSED_DENIED, ClaimStatus.CLOSED_WITHDRAWN]
                        ),
                        BaseClaim.closed_at >= period_start,
                        BaseClaim.closed_at <= period_end,
                    )
                )
                closed_claims_count = self.db.scalar(closed_claims_stmt) or 0

                # Add to results
                result_items.append(
                    ClaimsOverTimeItem(
                        period_start=period_start.date(),
                        new_claims_count=new_claims_count,
                        closed_claims_count=closed_claims_count,
                    )
                )
        else:  # weekly
            current_date = start_date
            while current_date <= end_date:
                period_end = current_date + timedelta(days=6, hours=23, minutes=59, seconds=59)
                if period_end > end_date:
                    period_end = end_date

                periods.append((current_date, period_end))
                current_date = current_date + timedelta(days=7)

            # Query data for each period
            result_items = []
            for period_start, period_end in periods:
                # New claims in period
                new_claims_stmt = (
                    select(func.count())
                    .select_from(BaseClaim)
                    .where(*claim_filters, BaseClaim.created_at >= period_start, BaseClaim.created_at <= period_end)
                )
                new_claims_count = self.db.scalar(new_claims_stmt) or 0

                # Closed claims in period
                closed_claims_stmt = (
                    select(func.count())
                    .select_from(BaseClaim)
                    .where(
                        *claim_filters,
                        BaseClaim.status.in_(
                            [ClaimStatus.CLOSED_SETTLED, ClaimStatus.CLOSED_DENIED, ClaimStatus.CLOSED_WITHDRAWN]
                        ),
                        BaseClaim.closed_at >= period_start,
                        BaseClaim.closed_at <= period_end,
                    )
                )
                closed_claims_count = self.db.scalar(closed_claims_stmt) or 0

                # Add to results
                result_items.append(
                    ClaimsOverTimeItem(
                        period_start=period_start.date(),
                        new_claims_count=new_claims_count,
                        closed_claims_count=closed_claims_count,
                    )
                )

        # Create column headers
        column_headers = [
            {"key": "period_start", "label": "Period"},
            {"key": "new_claims_count", "label": "New Claims"},
            {"key": "closed_claims_count", "label": "Closed Claims"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="claims_over_time", request=request, column_headers=column_headers
        )

        # Return report
        return ReportResponseClaimsOverTime(report_metadata=metadata, data=result_items)
