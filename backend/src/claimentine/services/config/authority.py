"""Service for managing customer-specific authority thresholds."""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.authority import AuthorityRole
from claimentine.models.config.authority import CustomerAuthorityThreshold
from claimentine.models.user import User
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class CustomerAuthorityThresholdService(BaseService):
    """Service for managing customer-specific authority thresholds."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def list_thresholds(
        self,
        customer_id: Optional[UUID] = None,
        authority_role: Optional[AuthorityRole] = None,
    ) -> List[CustomerAuthorityThreshold]:
        """List customer authority thresholds with optional filtering."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        stmt = select(CustomerAuthorityThreshold)

        # Apply filters
        if customer_id:
            stmt = stmt.where(CustomerAuthorityThreshold.customer_id == customer_id)
        if authority_role:
            stmt = stmt.where(CustomerAuthorityThreshold.authority_role == authority_role)

        # Filter out soft-deleted records
        stmt = stmt.where(CustomerAuthorityThreshold.is_deleted == False)

        return list(self.db.scalars(stmt))

    def get_threshold(self, threshold_id: UUID) -> Optional[CustomerAuthorityThreshold]:
        """Get a specific customer authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        threshold = self.db.get(CustomerAuthorityThreshold, threshold_id)
        if not threshold:
            raise NotFoundError(f"Customer authority threshold {threshold_id} not found")
        # Ensure soft-deleted are not returned
        if threshold.is_deleted:
            raise NotFoundError(f"Customer authority threshold {threshold_id} not found")

        return threshold

    def get_threshold_by_customer_and_role(
        self, customer_id: UUID, authority_role: AuthorityRole
    ) -> Optional[CustomerAuthorityThreshold]:
        """Get threshold for specific customer and authority role combination."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        return self.db.scalar(
            select(CustomerAuthorityThreshold).where(
                CustomerAuthorityThreshold.customer_id == customer_id,
                CustomerAuthorityThreshold.authority_role == authority_role,
                CustomerAuthorityThreshold.is_deleted == False,
            )
        )

    def create_threshold(
        self,
        customer_id: UUID,
        authority_role: AuthorityRole,
        reserve_limit: float,
        payment_limit: float,
        description: str = "",
    ) -> CustomerAuthorityThreshold:
        """Create a new customer-specific authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        # Check if threshold already exists, including soft-deleted ones
        existing_stmt = select(CustomerAuthorityThreshold).where(
            CustomerAuthorityThreshold.customer_id == customer_id,
            CustomerAuthorityThreshold.authority_role == authority_role,
        )
        existing = self.db.scalar(existing_stmt)

        if existing:
            if existing.is_deleted:
                # Undelete and update the soft-deleted record
                logger.info(
                    f"Found soft-deleted threshold for customer {customer_id}, role {authority_role}. Undeleting and updating.",
                    extra={"threshold_id": str(existing.id)},
                )
                existing.is_deleted = False
                existing.deleted_at = None
                existing.reserve_limit = reserve_limit
                existing.payment_limit = payment_limit
                existing.description = description
                threshold = existing  # Use the existing object
            else:
                # Active threshold already exists, raise error
                raise DuplicateError(
                    f"Authority threshold already exists for customer {customer_id}, role {authority_role}"
                )
        else:
            # Create new threshold
            threshold = CustomerAuthorityThreshold(
                customer_id=customer_id,
                authority_role=authority_role,
                reserve_limit=reserve_limit,
                payment_limit=payment_limit,
                description=description,
            )
            self.db.add(threshold)

        self.db.commit()
        self.db.refresh(threshold)

        logger.debug(
            f"Created customer authority threshold for customer {customer_id}, role {authority_role}",
            extra={
                "threshold_id": str(threshold.id),
                "customer_id": str(customer_id),
                "authority_role": str(authority_role),
                "reserve_limit": str(threshold.reserve_limit),
                "payment_limit": str(threshold.payment_limit),
            },
        )

        return threshold

    def update_threshold(
        self,
        threshold_id: UUID,
        reserve_limit: Optional[float] = None,
        payment_limit: Optional[float] = None,
        description: Optional[str] = None,
    ) -> CustomerAuthorityThreshold:
        """Update an existing customer authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        threshold = self.get_threshold(threshold_id)
        if not threshold:
            raise NotFoundError(f"Customer authority threshold {threshold_id} not found")

        # Update fields if provided
        if reserve_limit is not None:
            threshold.reserve_limit = reserve_limit
        if payment_limit is not None:
            threshold.payment_limit = payment_limit
        if description is not None:
            threshold.description = description

        self.db.commit()
        self.db.refresh(threshold)

        logger.debug(
            f"Updated customer authority threshold {threshold_id}",
            extra={
                "threshold_id": str(threshold.id),
                "customer_id": str(threshold.customer_id),
                "authority_role": str(threshold.authority_role),
                "reserve_limit": str(threshold.reserve_limit),
                "payment_limit": str(threshold.payment_limit),
            },
        )

        return threshold

    def delete_threshold(self, threshold_id: UUID) -> None:
        """Delete a customer authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="customer_authority_threshold")

        threshold = self.get_threshold(threshold_id)
        if not threshold:
            raise NotFoundError(f"Customer authority threshold {threshold_id} not found")

        # Soft delete: Set flags instead of deleting
        threshold.is_deleted = True
        threshold.deleted_at = datetime.utcnow()
        self.db.commit()

        logger.debug(
            f"Soft deleted customer authority threshold {threshold_id}",
            extra={
                "threshold_id": str(threshold.id),
                "customer_id": str(threshold.customer_id),
                "authority_role": str(threshold.authority_role),
            },
        )
