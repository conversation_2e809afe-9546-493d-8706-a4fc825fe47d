"""Service layer for Task management."""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload

from claimentine.core.audit import <PERSON>t<PERSON>elper
from claimentine.core.exceptions import AuthorizationError, DatabaseError, NotFoundError, ValidationError
from claimentine.db.session import get_db  # This might be unused if db comes from self
from claimentine.models.claim.base import BaseClaim
from claimentine.models.customer import Customer
from claimentine.models.task import Task, TaskPriority, TaskStatus
from claimentine.models.user import User
from claimentine.schemas.task import TaskCreate, TaskUpdate
from claimentine.services.base import BaseService  # Assuming BaseService exists
from claimentine.services.counter import get_next_task_number

logger = logging.getLogger(__name__)


class TaskService(BaseService):
    """Service for managing tasks."""

    def create(self, *, obj_in: TaskCreate, creator: User) -> Task:
        """Creates a new task, generates hr_id, and associates it with a claim and creator."""
        # Check permission for creating tasks
        self.check_permission("CREATE_TASK", resource_type="task")

        logger.debug(f"Attempting to create task titled '{obj_in.title}' for claim {obj_in.claim_id}")

        # --- Validation --- #
        # 1. Check if claim exists and get the associated customer
        claim = self.db.get(BaseClaim, obj_in.claim_id)
        if not claim:
            logger.warning(f"Claim {obj_in.claim_id} not found during task creation.")
            raise NotFoundError(f"Claim {obj_in.claim_id} not found")
        if not claim.customer_id:
            logger.error(f"Claim {obj_in.claim_id} is missing a customer_id.")
            raise ValidationError("Cannot create task: Associated claim is missing customer information.")

        # 2. Check if customer exists (and has prefix - crucial)
        customer = self.db.get(Customer, claim.customer_id)
        if not customer or not customer.prefix:
            logger.error(
                f"Customer {claim.customer_id} for claim {claim.id} not found or missing prefix during task creation."
            )
            raise ValidationError("Cannot create task: Customer information is incomplete or missing.")

        # 3. Check if assigned_to user exists (if provided)
        if obj_in.assigned_to:
            assignee = self.db.get(User, obj_in.assigned_to)
            if not assignee:
                logger.warning(f"Assignee user {obj_in.assigned_to} not found during task creation.")
                raise NotFoundError(f"User {obj_in.assigned_to} specified as assignee not found")

        # --- Generate hr_id --- #
        try:
            # Pass the already fetched customer object
            next_number = get_next_task_number(self.db, customer)
            hr_id = f"T-{customer.prefix}-{next_number:07d}"
            logger.info(f"Generated hr_id '{hr_id}' for new task for customer {customer.id}")
        except (
            SQLAlchemyError,
            Exception,
        ) as e:  # Removed NotFoundError as it's no longer raised by get_next_task_number
            # Log the actual exception details
            logger.exception(
                f"Error during get_next_task_number for customer {customer.id}. Type: {type(e).__name__}, Msg: {e}",
                exc_info=True,
            )
            # Re-raise the custom DatabaseError for the API layer
            raise DatabaseError("Failed to generate task ID. Please try again.") from e

        # --- Create Task Object --- #
        try:
            task_data = obj_in.model_dump()
            task = Task(
                **task_data,
                hr_id=hr_id,
                created_by=creator.id,
                # Use creator.id directly
            )
            self.db.add(task)
            self.db.flush()  # Ensure ID is available if generated by DB
            task_id = task.id  # Store the ID before commit might expire the object

            # Create audit entry for task creation
            audit_helper = AuditHelper(claim, creator.id)
            audit_helper.create_entry(
                entity_type="TASK",
                change_type="CREATE",
                entity_id=task.id,
                description=f"Task '{task.title}' created",
                new_value={
                    "title": task.title,
                    "description": task.description,
                    "priority": str(task.priority),
                    "status": str(task.status),
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "assigned_to": str(task.assigned_to) if task.assigned_to else None,
                },
            )

            self.db.commit()

            # Explicitly fetch the task with relationships loaded for the response model
            stmt = (
                select(Task)
                .where(Task.id == task_id)
                .options(selectinload(Task.creator), selectinload(Task.assignee), selectinload(Task.claim))
            )
            created_task_loaded = self.db.execute(stmt).scalar_one()

            # Add claim number to the task
            created_task_loaded.claim_number = claim.claim_number

            # self.db.refresh(task) # Remove refresh
            # Access relationships to trigger lazy loading for the response model # Remove lazy load attempts
            # _ = task.creator
            # _ = task.assignee # Load assignee as well if needed by TaskRead
            logger.info(f"Successfully created task {created_task_loaded.id} ('{created_task_loaded.hr_id}')")
            return created_task_loaded  # Return the fully loaded task
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.exception(f"Database error creating task: {e}", exc_info=True)
            raise DatabaseError("Could not create task due to a database error.") from e
        except Exception as e:
            self.db.rollback()
            logger.exception(f"Unexpected error creating task: {e}", exc_info=True)
            raise

    def _get_task_query(self, identifier: UUID | str):
        """Builds a query to fetch a task by UUID or hr_id, loading relationships."""
        options = [selectinload(Task.assignee), selectinload(Task.creator), selectinload(Task.claim)]
        stmt = select(Task).options(*options)

        if isinstance(identifier, UUID):
            stmt = stmt.where(Task.id == identifier)
        else:
            stmt = stmt.where(Task.hr_id == identifier)
        return stmt

    def resolve_task(self, *, task_identifier: str) -> Optional[Task]:
        """Fetches a task by its UUID or hr_id string."""
        # Check permission for viewing tasks
        self.check_permission("VIEW_TASKS", resource_type="task", resource_id=task_identifier)

        logger.debug(f"Resolving task with identifier: {task_identifier}")
        task: Optional[Task] = None
        try:
            # Try parsing as UUID first
            task_uuid = UUID(task_identifier)
            stmt = self._get_task_query(task_uuid)
            task = (self.db.execute(stmt)).scalar_one_or_none()
            logger.debug(f"Found task by UUID {task_uuid}: {'Yes' if task else 'No'}")
        except ValueError:
            # If not a valid UUID, assume it's an hr_id
            logger.debug(f"Identifier '{task_identifier}' is not a UUID, trying as hr_id.")
            stmt = self._get_task_query(task_identifier)
            task = (self.db.execute(stmt)).scalar_one_or_none()
            logger.debug(f"Found task by hr_id '{task_identifier}': {'Yes' if task else 'No'}")

        # Add claim number to the task if found
        if task and task.claim:
            task.claim_number = task.claim.claim_number

        return task

    def get(self, *, id: UUID) -> Optional[Task]:
        """Fetches a task by its UUID."""
        # Check permission for viewing tasks
        self.check_permission("VIEW_TASKS", resource_type="task", resource_id=id)

        logger.debug(f"Getting task by UUID: {id}")
        stmt = self._get_task_query(id)
        task = (self.db.execute(stmt)).scalar_one_or_none()

        # Add claim number to the task if found
        if task and task.claim:
            task.claim_number = task.claim.claim_number

        return task

    def get_by_hr_id(self, *, hr_id: str) -> Optional[Task]:
        """Fetches a task by its hr_id."""
        # Check permission for viewing tasks
        self.check_permission("VIEW_TASKS", resource_type="task", resource_id=hr_id)

        logger.debug(f"Getting task by hr_id: {hr_id}")
        stmt = self._get_task_query(hr_id)
        task = (self.db.execute(stmt)).scalar_one_or_none()

        # Add claim number to the task if found
        if task and task.claim:
            task.claim_number = task.claim.claim_number

        return task

    def get_multi_by_claim(self, *, claim_id: UUID, skip: int = 0, limit: int = 100) -> List[Task]:
        """Fetches multiple tasks associated with a specific claim."""
        # Check permission for listing tasks
        self.check_permission("LIST_TASKS", resource_type="task")

        logger.debug(f"Listing tasks for claim {claim_id} (skip={skip}, limit={limit})")

        # First get the claim to retrieve its claim_number
        claim_stmt = select(BaseClaim).where(BaseClaim.id == claim_id)
        claim = self.db.execute(claim_stmt).scalar_one_or_none()
        if not claim:
            return []

        stmt = (
            select(Task)
            .where(Task.claim_id == claim_id)
            .options(selectinload(Task.assignee), selectinload(Task.creator))
            .offset(skip)
            .limit(limit)
            .order_by(Task.created_at.desc())  # Example ordering
        )
        result = self.db.execute(stmt)
        tasks = list(result.scalars().all())

        # Add claim number to each task
        for task in tasks:
            task.claim_number = claim.claim_number

        return tasks

    def get_multi_assigned_to_user(self, *, user_id: UUID, skip: int = 0, limit: int = 100) -> List[Task]:
        """Fetches multiple tasks assigned to a specific user."""
        # Check permission for listing tasks
        self.check_permission("LIST_TASKS", resource_type="task")

        logger.debug(f"Listing tasks assigned to user {user_id} (skip={skip}, limit={limit})")
        stmt = (
            select(Task)
            .where(Task.assigned_to == user_id)
            .options(selectinload(Task.assignee), selectinload(Task.creator), selectinload(Task.claim))
            .offset(skip)
            .limit(limit)
            .order_by(Task.created_at.desc())
        )
        result = self.db.execute(stmt)
        tasks = list(result.scalars().all())

        # Add claim number to each task
        for task in tasks:
            if task.claim:
                task.claim_number = task.claim.claim_number

        return tasks

    def get_multi(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        claim_id: Optional[UUID] = None,
        assigned_to: Optional[UUID] = None,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,  # Added TaskPriority from models import
        title: Optional[str] = None,  # New parameter for title search
        # user_id_for_filtering: Optional[UUID] = None, # Add if filtering by ownership/assignment is needed
    ) -> tuple[List[Task], int]:
        """Fetches multiple tasks based on various optional filters and returns total count."""
        # Check permission for listing tasks
        self.check_permission("LIST_TASKS", resource_type="task")

        logger.debug(
            f"Listing tasks with filters: claim_id={claim_id}, assigned_to={assigned_to}, "
            f"status={status}, priority={priority}, title={title} (skip={skip}, limit={limit})"
        )

        # Build base query with filters
        base_stmt = select(Task)

        # Apply filters dynamically
        if claim_id is not None:
            base_stmt = base_stmt.where(Task.claim_id == claim_id)
        if assigned_to is not None:
            base_stmt = base_stmt.where(Task.assigned_to == assigned_to)
        if status is not None:
            base_stmt = base_stmt.where(Task.status == status)
        if priority is not None:
            base_stmt = base_stmt.where(Task.priority == priority)
        if title is not None:
            base_stmt = base_stmt.where(Task.title.ilike(f"%{title}%"))  # Case-insensitive partial matching

        # TODO: Add filtering based on user permissions if necessary
        # if user_id_for_filtering:
        #    base_stmt = base_stmt.where((Task.created_by == user_id_for_filtering) | (Task.assigned_to == user_id_for_filtering))

        try:
            # Get total count with the same filters
            count_stmt = select(func.count()).select_from(base_stmt.subquery())
            total_count = self.db.execute(count_stmt).scalar()

            # Get paginated results with eager loading
            stmt = (
                base_stmt.options(selectinload(Task.assignee), selectinload(Task.creator), selectinload(Task.claim))
                .offset(skip)
                .limit(limit)
                .order_by(Task.created_at.desc())
            )

            result = self.db.execute(stmt)
            tasks = list(result.scalars().all())
            logger.debug(f"Found {len(tasks)} tasks out of {total_count} total matching filters.")

            # Add claim number to each task
            for task in tasks:
                if task.claim:
                    task.claim_number = task.claim.claim_number

            return tasks, total_count
        except SQLAlchemyError as e:
            logger.exception(f"Database error listing tasks with filters: {e}", exc_info=True)
            raise DatabaseError("Could not list tasks due to a database error.") from e

    def update(self, *, db_obj: Task, obj_in: TaskUpdate) -> Task:
        """Updates a task with new data (excluding assignment/status changes)."""
        # Check permission for updating tasks
        self.check_permission("UPDATE_TASKS", resource_type="task", resource_id=db_obj.id)

        logger.debug(f"Updating task {db_obj.hr_id or db_obj.id}")
        update_data = obj_in.model_dump(exclude_unset=True)

        if not update_data:
            logger.warning(f"Update called for task {db_obj.hr_id or db_obj.id} with no data.")
            return db_obj  # No changes

        # Store previous values for audit
        previous_values = {}
        new_values = {}

        for field, value in update_data.items():
            previous_values[field] = getattr(db_obj, field)
            if field == "due_date" and previous_values[field]:
                previous_values[field] = previous_values[field].isoformat()
            elif previous_values[field] is not None and not isinstance(previous_values[field], (str, int, float, bool)):
                previous_values[field] = str(previous_values[field])

            new_values[field] = value
            if field == "due_date" and new_values[field]:
                new_values[field] = new_values[field].isoformat()
            elif new_values[field] is not None and not isinstance(new_values[field], (str, int, float, bool)):
                new_values[field] = str(new_values[field])

            setattr(db_obj, field, value)

        try:
            # Create audit entries for the update
            audit_helper = AuditHelper(db_obj.claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type="TASK",
                change_type="UPDATE",
                entity_id=db_obj.id,
                description=f"Task '{db_obj.title}' updated",
                previous_value=previous_values,
                new_value=new_values,
            )

            self.db.commit()
            self.db.refresh(db_obj)
            logger.info(f"Successfully updated task {db_obj.hr_id or db_obj.id}")
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.exception(f"Database error updating task {db_obj.hr_id or db_obj.id}: {e}", exc_info=True)
            raise DatabaseError("Could not update task due to a database error.") from e

    def assign(self, *, db_obj: Task, assignee_id: Optional[UUID]) -> Task:
        """Assigns or unassigns a task to a user."""
        # Check permission for assigning tasks
        self.check_permission("ASSIGN_TASK", resource_type="task", resource_id=db_obj.id)

        logger.debug(f"Assigning task {db_obj.hr_id or db_obj.id} to user {assignee_id}")

        # Validate assignee exists if not None
        if assignee_id:
            assignee = self.db.get(User, assignee_id)
            if not assignee:
                logger.warning(f"Assignee user {assignee_id} not found during task assignment.")
                raise NotFoundError(f"User {assignee_id} specified as assignee not found")

        # Store previous value for audit
        previous_assignee = db_obj.assigned_to

        # Update assignment
        db_obj.assigned_to = assignee_id

        try:
            # Create audit entry for assignment change
            audit_helper = AuditHelper(db_obj.claim, self.current_user.id if self.current_user else None)

            # Prepare description based on assignment change
            if previous_assignee is None and assignee_id is not None:
                description = f"Task '{db_obj.title}' assigned to user"
            elif previous_assignee is not None and assignee_id is None:
                description = f"Task '{db_obj.title}' unassigned"
            else:
                description = f"Task '{db_obj.title}' reassigned to different user"

            audit_helper.create_entry(
                entity_type="TASK",
                change_type="UPDATE",
                entity_id=db_obj.id,
                field_name="assigned_to",
                description=description,
                previous_value={"assigned_to": str(previous_assignee) if previous_assignee else None},
                new_value={"assigned_to": str(assignee_id) if assignee_id else None},
            )

            self.db.commit()
            self.db.refresh(db_obj)
            logger.info(f"Successfully assigned task {db_obj.hr_id or db_obj.id} to {assignee_id}")
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.exception(f"Database error assigning task {db_obj.hr_id or db_obj.id}: {e}", exc_info=True)
            raise DatabaseError("Could not assign task due to a database error.") from e

    def change_status(self, *, db_obj: Task, status: TaskStatus) -> Task:
        """Changes the status of a task, updating completed_at if necessary."""
        # Check permission for changing task status
        self.check_permission("CHANGE_TASK_STATUS", resource_type="task", resource_id=db_obj.id)

        logger.debug(f"Changing status of task {db_obj.hr_id or db_obj.id} to {status}")

        if db_obj.status == status:
            logger.warning(
                f"Task {db_obj.hr_id or db_obj.id} status change requested to current status {status}. No change."
            )
            return db_obj

        # Store previous status for audit
        previous_status = db_obj.status
        previous_completed_at = db_obj.completed_at.isoformat() if db_obj.completed_at else None

        # Update status and completed_at
        db_obj.status = status
        if status == TaskStatus.COMPLETED:
            db_obj.completed_at = datetime.utcnow()
            logger.info(f"Task {db_obj.hr_id or db_obj.id} marked as completed at {db_obj.completed_at}")
        else:
            db_obj.completed_at = None  # Ensure completed_at is null if not completed

        try:
            # Create audit entry for status change
            audit_helper = AuditHelper(db_obj.claim, self.current_user.id if self.current_user else None)

            # Build the previous and new values for audit
            previous_value = {"status": str(previous_status), "completed_at": previous_completed_at}

            new_value = {
                "status": str(status),
                "completed_at": db_obj.completed_at.isoformat() if db_obj.completed_at else None,
            }

            audit_helper.create_entry(
                entity_type="TASK",
                change_type="UPDATE",
                entity_id=db_obj.id,
                field_name="status",
                description=f"Task '{db_obj.title}' status changed from {previous_status} to {status}",
                previous_value=previous_value,
                new_value=new_value,
            )

            self.db.commit()
            self.db.refresh(db_obj)
            logger.info(f"Successfully changed status of task {db_obj.hr_id or db_obj.id} to {status}")
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.exception(f"Database error changing task status {db_obj.hr_id or db_obj.id}: {e}", exc_info=True)
            raise DatabaseError("Could not change task status due to a database error.") from e

    def remove(self, *, db_obj: Task) -> Task:
        """Deletes a task from the database (hard delete)."""
        # Check permission for deleting tasks
        self.check_permission("DELETE_TASK", resource_type="task", resource_id=db_obj.id)

        task_id = db_obj.id
        hr_id = db_obj.hr_id
        logger.debug(f"Deleting task {hr_id or task_id}")

        # Store task data for audit before deletion
        task_data = {
            "id": str(db_obj.id),
            "hr_id": db_obj.hr_id,
            "title": db_obj.title,
            "description": db_obj.description,
            "priority": str(db_obj.priority),
            "status": str(db_obj.status),
            "due_date": db_obj.due_date.isoformat() if db_obj.due_date else None,
            "assigned_to": str(db_obj.assigned_to) if db_obj.assigned_to else None,
            "created_by": str(db_obj.created_by) if db_obj.created_by else None,
            "created_at": db_obj.created_at.isoformat() if db_obj.created_at else None,
            "completed_at": db_obj.completed_at.isoformat() if db_obj.completed_at else None,
        }

        # Get a reference to the claim for audit entry after deletion
        claim = db_obj.claim

        try:
            # Create audit entry before deleting the task
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type="TASK",
                change_type="DELETE",
                entity_id=db_obj.id,
                description=f"Task '{db_obj.title}' deleted",
                previous_value=task_data,
            )

            self.db.delete(db_obj)
            self.db.commit()
            logger.info(f"Successfully deleted task {hr_id or task_id}")
            # The db_obj is expired after delete, so return a copy or key info if needed.
            # For now, we can return the (expired) object, but API layer should handle response.
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.exception(f"Database error deleting task {hr_id or task_id}: {e}", exc_info=True)
            raise DatabaseError("Could not delete task due to a database error.") from e
