"""API endpoints for metrics."""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query

from claimentine.api.v1.deps import get_metrics_service
from claimentine.core.auth import get_current_user
from claimentine.models.user import User
from claimentine.schemas.metrics.dashboard import DashboardMetricsResponse
from claimentine.services.metrics import MetricsService

router = APIRouter(prefix="/metrics", tags=["metrics"])


@router.get("/dashboard", response_model=DashboardMetricsResponse)
async def get_dashboard_metrics(
    period: str = Query("last_30_days", description="Time period for metrics"),
    compare_period: bool = Query(True, description="Compare with previous period"),
    customer_id: Optional[UUID] = Query(None, description="Filter by customer ID"),
    user_id: Optional[UUID] = Query(None, description="Filter by user ID"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user),
) -> DashboardMetricsResponse:
    """
    Get dashboard metrics.

    Retrieves key metrics for the dashboard, such as:
    - Total claims
    - Open claims
    - New claims in period
    - Closed claims in period
    - Average claim lifecycle
    - Total payments
    - Total reserves
    - Tasks pending
    - Tasks overdue
    - FNOLs pending

    The metrics can be filtered by customer_id or user_id, and compared
    with the previous period of equal length.
    """
    return metrics_service.get_dashboard_metrics(
        period=period, compare_period=compare_period, customer_id=customer_id, user_id=user_id
    )
