"""Customer API endpoints."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from claimentine.core.exceptions import NotFoundError
from claimentine.core.permissions import require_permissions
from claimentine.db.base import get_db
from claimentine.models.user import User
from claimentine.schemas.customer import CustomerCreate, CustomerResponse, CustomerUpdate
from claimentine.services.customer import CustomerService

api_router = APIRouter()


@api_router.get("", response_model=List[CustomerResponse], tags=["customers"])
def list_customers(
    active_only: bool = False,
    db: Session = Depends(get_db),
    _: User = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> List[CustomerResponse]:
    """List all customers."""
    return CustomerService(db).list_customers(active_only=active_only)


@api_router.get("/{customer_id}", response_model=CustomerResponse, tags=["customers"])
def get_customer(
    customer_id: UUID,
    db: Session = Depends(get_db),
    _: User = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> CustomerResponse:
    """Get a customer by ID."""
    customer = CustomerService(db).get_customer_by_id(customer_id)
    if not customer:
        raise NotFoundError(f"Customer {customer_id} not found")
    return customer


@api_router.post("", response_model=CustomerResponse, status_code=status.HTTP_201_CREATED, tags=["customers"])
def create_customer(
    data: CustomerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> CustomerResponse:
    """Create a new customer."""
    return CustomerService(db, current_user).create_customer(data)


@api_router.patch("/{customer_id}", response_model=CustomerResponse, tags=["customers"])
def update_customer(
    customer_id: UUID,
    data: CustomerUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> CustomerResponse:
    """Update a customer."""
    return CustomerService(db, current_user).update_customer(customer_id, data)


@api_router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["customers"])
def delete_customer(
    customer_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> None:
    """Delete a customer."""
    CustomerService(db, current_user).delete_customer(customer_id)
