"""API endpoints for customer-specific authority thresholds."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from claimentine.api.v1.deps import get_authority_threshold_service
from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import AuthorizationError, BadRequestError, DuplicateError, NotFoundError
from claimentine.db.base import get_db
from claimentine.models.authority import AuthorityRole
from claimentine.models.user import User
from claimentine.schemas.config.authority import (
    CustomerAuthorityThresholdCreate,
    CustomerAuthorityThresholdInDB,
    CustomerAuthorityThresholdUpdate,
)
from claimentine.services.config.authority import CustomerAuthorityThresholdService

api_router = APIRouter()


@api_router.get("/customers/{customer_id}/authority", response_model=List[CustomerAuthorityThresholdInDB])
def list_customer_authority_thresholds(
    customer_id: UUID,
    service: CustomerAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> List[CustomerAuthorityThresholdInDB]:
    """List authority thresholds for a specific customer. Permissions are checked at the service layer."""
    return service.list_thresholds(customer_id=customer_id)


@api_router.post(
    "/customers/{customer_id}/authority",
    response_model=CustomerAuthorityThresholdInDB,
    status_code=status.HTTP_201_CREATED,
)
def create_customer_authority_threshold(
    customer_id: UUID,
    data: CustomerAuthorityThresholdCreate,
    service: CustomerAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> CustomerAuthorityThresholdInDB:
    """Create a new authority threshold for a customer. Permissions are checked at the service layer."""
    # Ensure customer ID in URL matches the one in the request body
    if customer_id != data.customer_id:
        raise BadRequestError("Customer ID in URL must match the one in the request body")

    return service.create_threshold(
        customer_id=data.customer_id,
        authority_role=data.authority_role,
        reserve_limit=float(data.reserve_limit),
        payment_limit=float(data.payment_limit),
        description=data.description,
    )


@api_router.get("/authority/{threshold_id}", response_model=CustomerAuthorityThresholdInDB)
def get_customer_authority_threshold(
    threshold_id: UUID,
    service: CustomerAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> CustomerAuthorityThresholdInDB:
    """Get a specific customer authority threshold. Permissions are checked at the service layer."""
    threshold = service.get_threshold(threshold_id)
    if not threshold:
        raise NotFoundError(f"Authority threshold {threshold_id} not found")
    return threshold


@api_router.patch("/authority/{threshold_id}", response_model=CustomerAuthorityThresholdInDB)
def update_customer_authority_threshold(
    threshold_id: UUID,
    data: CustomerAuthorityThresholdUpdate,
    service: CustomerAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> CustomerAuthorityThresholdInDB:
    """Update a customer authority threshold. Permissions are checked at the service layer."""
    try:
        return service.update_threshold(
            threshold_id=threshold_id,
            reserve_limit=data.reserve_limit,
            payment_limit=data.payment_limit,
            description=data.description,
        )
    except NotFoundError as e:
        raise e


@api_router.delete("/authority/{threshold_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_customer_authority_threshold(
    threshold_id: UUID,
    service: CustomerAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> None:
    """Delete a customer authority threshold. Permissions are checked at the service layer."""
    service.delete_threshold(threshold_id)
