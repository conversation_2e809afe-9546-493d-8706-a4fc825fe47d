"""Customer schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.core.exceptions import ValidationError


class CustomerBase(BaseModel):
    """Base customer schema."""

    name: str = Field(..., description="Name of the customer/insurance company")
    prefix: str = Field(..., description="Unique 4-character prefix used in claim numbering")
    description: Optional[str] = Field(None, description="Additional details about the customer")
    active: bool = Field(True, description="Whether the customer is active in the system")

    @field_validator("prefix")
    @classmethod
    def validate_prefix(cls, v: str) -> str:
        """Validate prefix format."""
        if not v.isalnum() or len(v) != 4 or not v.isupper():
            raise ValidationError("Prefix must be exactly 4 uppercase alphanumeric characters")
        return v


class CustomerCreate(CustomerBase):
    """Create customer schema."""


class CustomerUpdate(BaseModel):
    """Update customer schema."""

    name: Optional[str] = Field(None, description="Name of the customer/insurance company")
    description: Optional[str] = Field(None, description="Additional details about the customer")
    active: Optional[bool] = Field(None, description="Whether the customer is active in the system")


class CustomerResponse(CustomerBase):
    """Customer response schema."""

    id: UUID = Field(..., description="Unique identifier for the customer")
    created_at: datetime = Field(..., description="When the customer was created")
    updated_at: datetime = Field(..., description="When the customer was last updated")

    model_config = ConfigDict(from_attributes=True)
