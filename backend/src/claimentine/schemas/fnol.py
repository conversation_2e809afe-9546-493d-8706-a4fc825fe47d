"""FNOL (First Notice of Loss) schemas."""

import re
from datetime import date, datetime, time
from enum import Enum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator, model_validator

from claimentine.core.exceptions import ValidationError
from claimentine.models.claim.base import ClaimStatus, ClaimType
from claimentine.models.claim.details import USState
from claimentine.schemas.claim.base import ClaimResponseSchema
from claimentine.schemas.customer import CustomerResponse


# Updated enum to match the model
class ReporterRelationship(str, Enum):
    """Relationship of the reporter to the claim/incident."""

    INSURED = "INSURED"
    CLAIMANT = "CLAIMANT"
    ATTORNEY = "ATTORNEY"
    AGENT = "AGENT"
    OTHER = "OTHER"


class CommunicationPreference(str, Enum):
    """Preferred method of communication for responses."""

    EMAIL = "EMAIL"
    PHONE = "PHONE"
    TEXT = "TEXT"
    MAIL = "MAIL"
    PORTAL = "PORTAL"
    NO_PREFERENCE = "NO_PREFERENCE"


class FNOLBase(BaseModel):
    """Base FNOL schema."""

    customer_id: UUID = Field(..., description="ID of the customer the FNOL belongs to")
    reported_by: str = Field(..., max_length=200, description="Name of the person reporting the loss")
    description: Optional[str] = Field(None, description="Detailed description of the incident")
    incident_date: Optional[date] = Field(None, description="Date when the incident occurred")
    incident_location: Optional[str] = Field(
        None, max_length=500, description="Physical location where the incident occurred"
    )
    policy_number: Optional[str] = Field(None, max_length=100, description="Policy number associated with the FNOL")

    # Updated contact fields - separate phone and email
    reporter_phone: Optional[str] = Field(
        None,
        max_length=20,
        description="Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')",
    )
    reporter_email: Optional[EmailStr] = Field(
        None, description="Reporter's email address (e.g., '<EMAIL>')"
    )

    # New incident state field - required
    incident_state: USState = Field(..., description="US state where the incident occurred")

    # Existing fields
    incident_time: Optional[time] = Field(
        None, description="Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"
    )
    reporter_relationship: Optional[ReporterRelationship] = Field(
        None, description="Relationship of the reporter to the claim/incident"
    )
    communication_preference: Optional[CommunicationPreference] = Field(
        None, description="Preferred method of communication for responses"
    )

    @field_validator("incident_date")
    @classmethod
    def validate_incident_date(cls, value):
        """Validate that incident_date is within reasonable bounds."""
        if value is None:
            return value

        today = date.today()

        # Check if date is in the future
        if value > today:
            raise ValidationError("Incident date cannot be in the future")

        # Check if date is unreasonably old (before 1900)
        if value.year < 1900:
            raise ValidationError("Incident date cannot be before 1900")

        return value

    @field_validator("reporter_phone")
    @classmethod
    def validate_reporter_phone(cls, value):
        """Validate US phone number format."""
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"[^\d]", "", value)

        # Check if it's a valid US phone number (10 digits, optionally with country code)
        if len(digits_only) == 10:
            # Valid 10-digit US number
            return value
        elif len(digits_only) == 11 and digits_only.startswith("1"):
            # Valid US number with country code
            return value
        else:
            raise ValidationError(
                "Phone number must be a valid US format (e.g., '************', '(*************', or '5551234567')"
            )

    @model_validator(mode="after")
    def validate_contact_info(self):
        """Ensure at least one contact method (phone or email) is provided."""
        if not self.reporter_phone and not self.reporter_email:
            raise ValidationError("At least one contact method (reporter_phone or reporter_email) must be provided")
        return self


class FNOLCreate(FNOLBase):
    """Create FNOL schema."""

    fnol_number: Optional[str] = Field(
        None,
        pattern=r"^[A-Z0-9]{4}-FNOL-\d{7}$",
        description="Unique FNOL identifier in format PREFIX-FNOL-NNNNNNN (auto-generated if not provided)",
    )


class FNOLUpdate(BaseModel):
    """Update FNOL schema."""

    reported_by: Optional[str] = Field(None, max_length=200, description="Name of the person reporting the loss")
    description: Optional[str] = Field(None, description="Detailed description of the incident")
    incident_date: Optional[date] = Field(None, description="Date when the incident occurred")
    incident_location: Optional[str] = Field(
        None, max_length=500, description="Physical location where the incident occurred"
    )
    policy_number: Optional[str] = Field(None, max_length=100, description="Policy number associated with the FNOL")

    # Updated contact fields - separate phone and email
    reporter_phone: Optional[str] = Field(
        None,
        max_length=20,
        description="Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')",
    )
    reporter_email: Optional[EmailStr] = Field(
        None, description="Reporter's email address (e.g., '<EMAIL>')"
    )

    # New incident state field
    incident_state: Optional[USState] = Field(None, description="US state where the incident occurred")

    # Existing fields
    incident_time: Optional[time] = Field(
        None, description="Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"
    )
    reporter_relationship: Optional[ReporterRelationship] = Field(
        None, description="Relationship of the reporter to the claim/incident"
    )
    communication_preference: Optional[CommunicationPreference] = Field(
        None, description="Preferred method of communication for responses"
    )

    @field_validator("incident_date")
    @classmethod
    def validate_incident_date(cls, value):
        """Validate that incident_date is within reasonable bounds."""
        if value is None:
            return value

        today = date.today()

        # Check if date is in the future
        if value > today:
            raise ValidationError("Incident date cannot be in the future")

        # Check if date is unreasonably old (before 1900)
        if value.year < 1900:
            raise ValidationError("Incident date cannot be before 1900")

        return value

    @field_validator("reporter_phone")
    @classmethod
    def validate_reporter_phone(cls, value):
        """Validate US phone number format."""
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"[^\d]", "", value)

        # Check if it's a valid US phone number (10 digits, optionally with country code)
        if len(digits_only) == 10:
            # Valid 10-digit US number
            return value
        elif len(digits_only) == 11 and digits_only.startswith("1"):
            # Valid US number with country code
            return value
        else:
            raise ValidationError(
                "Phone number must be a valid US format (e.g., '************', '(*************', or '5551234567')"
            )


class FNOLResponse(FNOLBase):
    """FNOL response schema."""

    id: UUID = Field(..., description="Unique identifier for the FNOL")
    fnol_number: str = Field(..., description="Unique FNOL reference number")
    reported_at: datetime = Field(..., description="Timestamp when the loss was reported")
    created_at: datetime = Field(..., description="When the FNOL was created in the system")
    updated_at: datetime = Field(..., description="When the FNOL was last updated")
    customer: CustomerResponse = Field(..., description="Customer details")
    claims: List[ClaimResponseSchema] = Field(default_factory=list, description="Claims associated with this FNOL")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "fnol_number": "SLCT-FNOL-0000001",
                "customer_id": "123e4567-e89b-12d3-a456-************",
                "reported_by": "John Smith",
                "reported_at": "2024-01-01T00:00:00Z",
                "description": "Multi-vehicle accident on highway",
                "incident_date": "2024-01-01",
                "incident_location": "I-95 North, Mile Marker 123",
                "policy_number": "POL123456789",
                "reporter_phone": "************",
                "reporter_email": "<EMAIL>",
                "incident_state": "FL",
                "incident_time": "14:30:00",
                "reporter_relationship": "INSURED",
                "communication_preference": "EMAIL",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "customer": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "Selective Insurance",
                    "prefix": "SLCT",
                    "description": "Selective Insurance Company of America",
                    "active": True,
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                },
                "claims": [],
            }
        },
    )


class FNOLConversionResponse(BaseModel):
    """Schema to use specifically for FNOL to claim conversion to avoid circular references."""

    claim_id: UUID = Field(..., description="Unique identifier for the created claim")
    claim_number: str = Field(..., description="Unique claim reference number")
    claim_type: ClaimType = Field(..., description="Type of the created claim")
    status: ClaimStatus = Field(..., description="Status of the created claim")

    # Pydantic configuration
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "claim_id": "123e4567-e89b-12d3-a456-************",
                "claim_number": "SLCT-2024-0000001",
                "claim_type": "AUTO",
                "status": "DRAFT",
            }
        },
    )

    # Static factory methods to create from different claim types
    @classmethod
    def from_auto_claim(cls, claim):
        """Create response from an auto claim."""
        return cls(claim_id=claim.id, claim_number=claim.claim_number, claim_type=ClaimType.AUTO, status=claim.status)

    @classmethod
    def from_property_claim(cls, claim):
        """Create response from a property claim."""
        return cls(
            claim_id=claim.id, claim_number=claim.claim_number, claim_type=ClaimType.PROPERTY, status=claim.status
        )

    @classmethod
    def from_gl_claim(cls, claim):
        """Create response from a general liability claim."""
        return cls(
            claim_id=claim.id,
            claim_number=claim.claim_number,
            claim_type=ClaimType.GENERAL_LIABILITY,
            status=claim.status,
        )
