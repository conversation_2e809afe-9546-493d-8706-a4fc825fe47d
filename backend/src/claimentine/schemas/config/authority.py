"""Schemas for customer-specific authority thresholds."""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from claimentine.models.authority import AuthorityRole


class CustomerAuthorityThresholdBase(BaseModel):
    """Base schema for customer-specific authority thresholds."""

    authority_role: AuthorityRole
    reserve_limit: Decimal
    payment_limit: Decimal
    description: Optional[str] = None


class CustomerAuthorityThresholdCreate(CustomerAuthorityThresholdBase):
    """Schema for creating customer-specific authority thresholds."""

    customer_id: UUID


class CustomerAuthorityThresholdUpdate(BaseModel):
    """Schema for updating customer-specific authority thresholds."""

    reserve_limit: Optional[Decimal] = None
    payment_limit: Optional[Decimal] = None
    description: Optional[str] = None


class CustomerAuthorityThresholdInDB(CustomerAuthorityThresholdBase):
    """Schema for customer-specific authority thresholds in database."""

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            # Format decimal values with exactly 2 decimal places
            Decimal: lambda v: f"{v:.2f}"
        },
    )

    id: UUID
    customer_id: UUID
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
