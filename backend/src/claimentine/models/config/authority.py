"""Customer-specific authority threshold configurations."""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, UniqueConstraint, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.authority import AuthorityRole


class CustomerAuthorityThreshold(Base):
    """Customer-specific authority thresholds for financial operations."""

    __tablename__ = "customer_authority_thresholds"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    customer_id: Mapped[UUID] = mapped_column(ForeignKey("customers.id", ondelete="CASCADE"), nullable=False)
    authority_role: Mapped[AuthorityRole] = mapped_column(nullable=False)
    reserve_limit: Mapped[Decimal] = mapped_column(nullable=False)
    payment_limit: Mapped[Decimal] = mapped_column(nullable=False)
    description: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    customer = relationship("Customer", back_populates="authority_thresholds")

    __table_args__ = (
        # Ensure there's only one threshold per customer+role combination
        UniqueConstraint("customer_id", "authority_role", name="uq_customer_authority_role"),
    )

    def __repr__(self) -> str:
        """String representation of CustomerAuthorityThreshold."""
        return f"<CustomerAuthorityThreshold {self.authority_role} for customer {self.customer_id} (reserve: {self.reserve_limit})>"
