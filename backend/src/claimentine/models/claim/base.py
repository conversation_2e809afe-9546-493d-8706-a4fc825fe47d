"""Base claim model."""

from datetime import date, datetime
from enum import Enum
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, CheckConstraint, Date, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import ForeignKey, Index, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.core.exceptions import ValidationError
from claimentine.db.base_class import Base
from claimentine.models.claim.details import USState
from claimentine.models.claim.financial import ClaimFinancials
from claimentine.models.customer import Customer
from claimentine.models.fnol import FNOL
from claimentine.models.status_history import ClaimStatusHistory


class ClaimType(str, Enum):
    """Types of claims."""

    AUTO = "AUTO"
    PROPERTY = "PROPERTY"
    GENERAL_LIABILITY = "GENERAL_LIABILITY"  # Renamed from LIABILITY for clarity


class ClaimStatus(str, Enum):
    """Status of a claim."""

    DRAFT = "DRAFT"  # Initial FNOL, data collection incomplete
    INVESTIGATION = "INVESTIGATION"  # Initial investigation in progress
    SETTLEMENT = "SETTLEMENT"  # Settlement being negotiated
    LITIGATION = "LITIGATION"  # In litigation
    RECOVERY = "RECOVERY"  # Recovery being pursued
    CLOSED_SETTLED = "CLOSED_SETTLED"  # Closed, settled with payment
    CLOSED_DENIED = "CLOSED_DENIED"  # Closed, claim denied
    CLOSED_WITHDRAWN = "CLOSED_WITHDRAWN"  # Closed, claim withdrawn


class RecoveryStatus(str, Enum):
    """Status of recovery efforts."""

    NOT_STARTED = "NOT_STARTED"
    INITIATED = "INITIATED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"


class BaseClaim(Base):
    """Base claim model with essential fields."""

    __tablename__ = "base_claims"
    __mapper_args__ = {"polymorphic_identity": None, "polymorphic_on": "type"}

    # Table constraints
    __table_args__ = (
        CheckConstraint(
            "(reporter_phone IS NOT NULL) OR (reporter_email IS NOT NULL)",
            name="check-reporter-phone-or-email",
        ),
        # Composite indexes for optimized queries
        Index("idx_claims_customer_status", "customer_id", "status"),
        Index("idx_claims_assigned_status", "assigned_to_id", "status"),
        Index("idx_claims_incident_date_status", "incident_date", "status"),
        Index("idx_claims_type_status", "type", "status"),
        Index("idx_claims_jurisdiction_status", "jurisdiction", "status"),
        Index("idx_claims_reporter_phone", "reporter_phone"),
        Index("idx_claims_reporter_email", "reporter_email"),
        Index("idx_claims_claimant_phone", "claimant_phone"),
        Index("idx_claims_claimant_email", "claimant_email"),
        Index("idx_claims_insured_phone", "insured_phone"),
        Index("idx_claims_insured_email", "insured_email"),
        Index("idx_claims_incident_location", "incident_location"),
    )

    # Required fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    customer_id: Mapped[UUID] = mapped_column(
        ForeignKey("customers.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    claim_number: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    type: Mapped[ClaimType] = mapped_column(nullable=False, index=True)
    status: Mapped[ClaimStatus] = mapped_column(nullable=False, default=ClaimStatus.DRAFT, index=True)

    # Contact person fields - either claimant or insured (or both) should be provided
    claimant_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, index=True)
    insured_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, index=True)

    # Reporter contact fields (new)
    reporter_phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    reporter_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Optional fields
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    claimant_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    claimant_phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    insured_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    insured_phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    incident_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, index=True)
    jurisdiction: Mapped[Optional[USState]] = mapped_column(SQLAlchemyEnum(USState), nullable=True)
    incident_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    policy_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)

    # Recovery fields
    recovery_status: Mapped[Optional[RecoveryStatus]] = mapped_column(
        default=RecoveryStatus.NOT_STARTED, nullable=True, index=True
    )
    carrier_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    carrier_contact: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    carrier_claim_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    carrier_adjuster: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    expected_amount: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    received_amount: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # Assignment
    assigned_to_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    supervisor_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    created_by_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )

    # FNOL relationship
    fnol_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("fnols.id", ondelete="SET NULL"), nullable=True, index=True
    )
    fnol: Mapped[Optional["FNOL"]] = relationship("FNOL", back_populates="claims")

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"), index=True)
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )
    closed_at: Mapped[Optional[datetime]] = mapped_column(nullable=True, index=True)

    # Relationships
    customer: Mapped["Customer"] = relationship("Customer")
    documents: Mapped[List["Document"]] = relationship("Document", back_populates="claim", cascade="all, delete-orphan")
    notes: Mapped[List["Note"]] = relationship("Note", back_populates="claim", cascade="all, delete-orphan")
    tasks: Mapped[List["Task"]] = relationship("Task", back_populates="claim", cascade="all, delete-orphan")
    status_history: Mapped[List["ClaimStatusHistory"]] = relationship(
        "ClaimStatusHistory", back_populates="claim", cascade="all, delete-orphan"
    )

    # New relationships for additional data tracking
    witnesses: Mapped[List["Witness"]] = relationship("Witness", back_populates="claim", cascade="all, delete-orphan")
    attorneys: Mapped[List["Attorney"]] = relationship("Attorney", back_populates="claim", cascade="all, delete-orphan")
    audit_trail: Mapped[List["AuditTrail"]] = relationship(
        "AuditTrail", back_populates="claim", cascade="all, delete-orphan"
    )

    # Type-specific details relationships
    auto_details: Mapped[Optional["AutoDetails"]] = relationship(
        "AutoDetails", back_populates="claim", cascade="all, delete-orphan", uselist=False
    )
    property_details: Mapped[Optional["PropertyDetails"]] = relationship(
        "PropertyDetails", back_populates="claim", cascade="all, delete-orphan", uselist=False
    )
    gl_details: Mapped[Optional["GeneralLiabilityDetails"]] = relationship(
        "GeneralLiabilityDetails", back_populates="claim", cascade="all, delete-orphan", uselist=False
    )

    # Financial details relationship
    financials: Mapped[Optional["ClaimFinancials"]] = relationship(
        "ClaimFinancials", back_populates="claim", cascade="all, delete-orphan", uselist=False
    )

    # User relationships
    assigned_to: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[assigned_to_id], back_populates="assigned_claims"
    )
    supervisor: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[supervisor_id], back_populates="supervised_claims"
    )
    created_by: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[created_by_id], back_populates="created_claims"
    )

    def __repr__(self) -> str:
        """String representation."""
        return f"<{self.__class__.__name__} {self.claim_number}>"

    def transition_to(
        self, new_status: ClaimStatus, changed_by_id: Optional[UUID] = None, reason: Optional[str] = None
    ) -> None:
        """Handle claim status transition."""
        old_status = self.status

        # Validate the transition
        if old_status == new_status:
            return  # No-op, status is already set

        # Handle special transitions
        if new_status in (
            ClaimStatus.CLOSED_SETTLED,
            ClaimStatus.CLOSED_DENIED,
            ClaimStatus.CLOSED_WITHDRAWN,
        ):
            # Set closed_at timestamp for all closing statuses
            self.closed_at = datetime.now()

        # Update status
        self.status = new_status

        # Create status history entry only if the claim has been saved (has an ID)
        # Otherwise, the history will be created when the claim is first saved
        if self.id is not None:
            history_entry = ClaimStatusHistory(
                claim_id=self.id,
                from_status=old_status.value,
                to_status=new_status.value,
                reason=reason,
                changed_by=changed_by_id,
            )

            # Add to the status_history relationship so it's saved in the same transaction
            self.status_history.append(history_entry)

    def create_initial_status_history(self, created_by_id: Optional[UUID] = None, reason: Optional[str] = None) -> None:
        """Create the initial status history entry for a new claim.

        This should be called after the claim is saved to the database and has an ID.

        Args:
            created_by_id: ID of the user who created the claim (optional)
            reason: Reason for the initial status (optional)
        """
        if self.id is None:
            raise ValidationError("Claim must be saved to the database before creating status history")

        # Create the initial status history entry with no previous status
        history_entry = ClaimStatusHistory(
            claim_id=self.id,
            from_status=None,  # No previous status for initial entry
            to_status=self.status.value,
            reason=reason or "Initial claim creation",
            changed_by=created_by_id,
        )

        # Add to the status_history relationship
        self.status_history.append(history_entry)

    def create_audit_entry(
        self,
        entity_type: str,
        change_type: str,
        entity_id: Optional[UUID] = None,
        field_name: Optional[str] = None,
        previous_value: Optional[Dict] = None,
        new_value: Optional[Dict] = None,
        description: Optional[str] = None,
        changed_by_id: Optional[UUID] = None,
    ) -> None:
        """Create an audit trail entry for this claim.

        This method should be called after any significant change to the claim or related entities.

        Args:
            entity_type: Type of entity being audited (e.g., "CLAIM", "WITNESS")
            change_type: Type of change (CREATE, UPDATE, DELETE)
            entity_id: ID of the specific entity if applicable
            field_name: Name of the field that changed
            previous_value: Previous value of the field
            new_value: New value of the field
            description: Human-readable description of the change
            changed_by_id: ID of the user who made the change

        Raises:
            ValidationError: If the claim is not saved or other validation fails
        """
        import logging

        logger = logging.getLogger(__name__)

        if self.id is None:
            raise ValidationError("Claim must be saved to the database before creating audit entries")

        # Import here to avoid circular imports
        from claimentine.models.audit import AuditTrail, ChangeType, EntityType

        try:
            # Create the audit trail entry
            audit_entry = AuditTrail(
                claim_id=self.id,
                entity_type=EntityType(entity_type),
                change_type=ChangeType(change_type),
                entity_id=entity_id,
                field_name=field_name,
                previous_value=previous_value,
                new_value=new_value,
                description=description,
                changed_by_id=changed_by_id,
            )

            # Add to the audit_trail relationship
            self.audit_trail.append(audit_entry)
        except Exception as e:
            logger.error(f"Error creating audit entry for claim {self.claim_number}: {str(e)}")
            raise ValidationError(f"Failed to create audit entry: {str(e)}")

    def get_audit_entries(
        self,
        skip: int = 0,
        limit: int = 50,
        entity_type: Optional[str] = None,
        change_type: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        changed_by_id: Optional[UUID] = None,
    ) -> Dict:
        """Get audit trail entries for this claim with pagination and filtering.

        Args:
            skip: Number of entries to skip (for pagination)
            limit: Maximum number of entries to return (for pagination)
            entity_type: Filter by entity type
            change_type: Filter by change type
            from_date: Filter by date range start
            to_date: Filter by date range end
            changed_by_id: Filter by user who made the change

        Returns:
            Dict with items, total count, and pagination metadata
        """
        import logging
        from datetime import timedelta

        logger = logging.getLogger(__name__)

        # Import here to avoid circular imports
        from sqlalchemy import func, select
        from sqlalchemy.orm import Session

        from claimentine.models.audit import AuditTrail, ChangeType, EntityType

        # Get current session
        session = Session.object_session(self)
        if not session:
            raise ValidationError("Claim must be attached to a session to get audit entries")

        # Build base query
        query = select(AuditTrail).where(AuditTrail.claim_id == self.id)

        # Apply filters
        if entity_type:
            query = query.where(AuditTrail.entity_type == EntityType(entity_type))

        if change_type:
            query = query.where(AuditTrail.change_type == ChangeType(change_type))

        if from_date:
            query = query.where(AuditTrail.changed_at >= from_date)

        if to_date:
            # Add 1 day to include the to_date
            end_date = to_date + timedelta(days=1)
            query = query.where(AuditTrail.changed_at < end_date)

        if changed_by_id:
            query = query.where(AuditTrail.changed_by_id == changed_by_id)

        # Order by most recent first
        query = query.order_by(AuditTrail.changed_at.desc())

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total = session.scalar(count_query)

        # Apply pagination
        paginated_query = query.offset(skip).limit(limit)

        try:
            # Execute query
            items = list(session.scalars(paginated_query))

            # Return paginated response
            return {"items": items, "total": total, "skip": skip, "limit": limit}
        except Exception as e:
            logger.error(f"Error retrieving audit entries for claim {self.claim_number}: {str(e)}")
            raise ValidationError(f"Failed to get audit entries: {str(e)}")
