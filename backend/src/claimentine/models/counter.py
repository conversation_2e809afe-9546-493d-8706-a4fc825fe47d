"""Model for managing per-customer counters."""

from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.customer import Customer  # Import Customer


class CustomerTaskCounter(Base):
    """Stores the last used task number for a specific customer."""

    __tablename__ = "customer_task_counters"

    # The customer_id is the primary key and a foreign key to the customers table.
    customer_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("customers.id"), primary_key=True  # Change users.id to customers.id
    )
    last_task_number: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Update relationship to point to Customer
    # Note: Check Customer model for back_populates if needed
    customer: Mapped["Customer"] = relationship("Customer")

    def __repr__(self) -> str:
        """String representation of the counter."""
        return f"<CustomerTaskCounter customer_id={self.customer_id} last_task_number={self.last_task_number}>"


# Add relationships if needed in the future, e.g., backref from User
# user = relationship("User", back_populates="task_counter")
