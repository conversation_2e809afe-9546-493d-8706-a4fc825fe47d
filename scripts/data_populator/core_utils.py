"""Core utilities for the data populator script.

This module provides essential functions for:
- Database reset and setup
- CLI command execution
- Data generation helpers
- Entity creation functions
"""

import json
import logging
import os
import subprocess
import sys
from decimal import Decimal
from typing import Any, Dict, List, Optional, Set
from pathlib import Path

import bcrypt
from sqlalchemy import create_engine, select
from sqlalchemy.orm import Session

# Import backend modules for direct database operations
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend/src'))

from claimentine.core.config import settings
from claimentine.models.associations import UserPermission
from claimentine.models.auth import ROLE_PERMISSIONS, Permission
from claimentine.models.authority import AuthorityLevel, AuthorityRole
from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType
from claimentine.models.config.reserve import ReserveConfiguration
from claimentine.models.user import User, UserRole, UserStatus
from claimentine.scripts.setup_db import create_schema_objects

import random
from datetime import datetime, timedelta
from faker import Faker

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Store for tracking created entity IDs across the population process
created_data: Dict[str, Any] = {
    "users": {},
    "customers": {},
    "fnols": {},
    "claims": {},
    "notes": {},
    "tasks": {},
    "witnesses": {},
    "attorneys": {},
}

# Initialize Faker for realistic data generation
fake = Faker()

# Duplicate essential data from insert_initial_data.py
PERMISSION_DESCRIPTIONS = {
    # Claims - Basic
    "VIEW_OWN_CLAIMS": "Ability to view own claims",
    "CREATE_OWN_CLAIMS": "Ability to create own claims",
    "CREATE_CLAIMS": "Ability to create claims on behalf of others",
    "EDIT_ASSIGNED_CLAIMS": "Ability to edit assigned claims",
    "EDIT_CLAIMS": "Ability to edit any claim",
    "DELETE_CLAIMS": "Ability to delete claims",
    "ASSIGN_CLAIMS": "Ability to assign claims to others",
    "UNASSIGN_CLAIMS": "Ability to unassign claims from users",
    "CLOSE_CLAIMS": "Ability to close claims",
    "CHANGE_CLAIM_STATUS": "Ability to change claim status",
    "VIEW_ASSIGNED_CLAIMS": "Ability to view assigned claims",
    "VIEW_ALL_CLAIMS": "Ability to view all claims",
    # Documents
    "VIEW_OWN_DOCUMENTS": "Ability to view own documents",
    "VIEW_ASSIGNED_DOCUMENTS": "Ability to view documents of assigned claims",
    "VIEW_ALL_DOCUMENTS": "Ability to view all documents",
    "UPLOAD_OWN_DOCUMENTS": "Ability to upload documents to own claims",
    "UPLOAD_DOCUMENTS": "Ability to upload documents to any claim",
    "DELETE_DOCUMENTS": "Ability to delete documents",
    "MANAGE_DOCUMENT_CATEGORIES": "Ability to manage document categories",
    "VIEW_CLAIM_DOCUMENTS": "Ability to view documents of specific claims",
    "UPLOAD_CLAIM_DOCUMENTS": "Ability to upload documents to specific claims",
    "EDIT_CLAIM_DOCUMENTS": "Ability to edit documents of specific claims",
    "DELETE_CLAIM_DOCUMENTS": "Ability to delete documents of specific claims",
    # Profile & Users
    "VIEW_OWN_PROFILE": "Ability to view own profile",
    "EDIT_OWN_PROFILE": "Ability to edit own profile",
    "VIEW_USERS": "Ability to view user list",
    "CREATE_USERS": "Ability to create new users",
    "EDIT_USERS": "Ability to edit users",
    "DELETE_USERS": "Ability to delete users",
    "MANAGE_ROLES": "Ability to manage user roles",
    # Customers
    "VIEW_CUSTOMERS": "Ability to view customer information",
    "CREATE_CUSTOMERS": "Ability to create new customers",
    "EDIT_CUSTOMERS": "Ability to edit customer information",
    "DELETE_CUSTOMERS": "Ability to delete customers",
    # Communications & Tasks
    "SEND_COMMUNICATIONS": "Ability to send communications",
    "MANAGE_COMMUNICATIONS": "Ability to manage all communications",
    "MANAGE_TASKS": "Ability to manage tasks",
    # Tasks - Granular
    "CREATE_TASK": "Ability to create tasks",
    "VIEW_ASSIGNED_TASKS": "Ability to view tasks assigned to self or created by self",
    "VIEW_ALL_TASKS": "Ability to view all tasks",
    "EDIT_ASSIGNED_TASKS": "Ability to edit tasks assigned to self or created by self",
    "EDIT_ALL_TASKS": "Ability to edit all tasks",
    "ASSIGN_TASK": "Ability to assign/reassign tasks",
    "DELETE_TASK": "Ability to delete tasks",
    "VIEW_TASKS": "Ability to view tasks",
    "LIST_TASKS": "Ability to list tasks",
    "UPDATE_TASKS": "Ability to update task details",
    "CHANGE_TASK_STATUS": "Ability to change task status",
    # Financial Operations
    "VIEW_CLAIM_FINANCIALS": "Ability to view claim financials",
    "SET_INITIAL_RESERVE": "Ability to set initial claim reserves",
    "UPDATE_RESERVES": "Ability to update claim reserves",
    "PROCESS_PAYMENTS": "Ability to process claim payments",
    "OVERRIDE_FINANCIAL_LIMITS": "Ability to override financial limits",
    # Metrics
    "VIEW_METRICS": "Ability to view system metrics and KPIs",
    # Reports & Analytics
    "VIEW_REPORTS": "Ability to view standard reports",
    "VIEW_BASIC_ANALYTICS": "Ability to view basic analytics",
    "VIEW_ADVANCED_ANALYTICS": "Ability to view advanced analytics",
    "CONFIGURE_REPORTS": "Ability to configure and create reports",
    "MANAGE_WORKFLOWS": "Ability to manage claim workflows",
    # System & Security
    "SYSTEM_CONFIGURATION": "Ability to modify system configuration",
    "MANAGE_PERMISSIONS": "Ability to manage system permissions",
    "VIEW_AUDIT_LOGS": "Ability to view audit logs",
    "MANAGE_API_KEYS": "Ability to manage API keys",
    "MANAGE_SECURITY_SETTINGS": "Ability to manage security settings",
    "VIEW_SECURITY_LOGS": "Ability to view security logs",
}

INITIAL_AUTHORITY_LEVELS = {
    AuthorityRole.UNLIMITED: {
        "reserve_limit": Decimal("1000000"),  # $1M
        "payment_limit": Decimal("1000000"),  # $1M
        "description": "Unlimited authority for all financial operations",
    },
    AuthorityRole.MANAGER: {
        "reserve_limit": Decimal("750000"),  # $750K
        "payment_limit": Decimal("750000"),  # $750K
        "description": "High authority for most financial operations",
    },
    AuthorityRole.SUPERVISOR: {
        "reserve_limit": Decimal("500000"),  # $500K
        "payment_limit": Decimal("500000"),  # $500K
        "description": "Supervisory authority for significant financial operations",
    },
    AuthorityRole.SENIOR: {
        "reserve_limit": Decimal("250000"),  # $250K
        "payment_limit": Decimal("250000"),  # $250K
        "description": "Senior level authority for substantial financial operations",
    },
    AuthorityRole.INTERMEDIATE: {
        "reserve_limit": Decimal("100000"),  # $100K
        "payment_limit": Decimal("100000"),  # $100K
        "description": "Intermediate authority for moderate financial operations",
    },
    AuthorityRole.BASIC: {
        "reserve_limit": Decimal("25000"),  # $25K
        "payment_limit": Decimal("25000"),  # $25K
        "description": "Basic authority for routine financial operations",
    },
    AuthorityRole.NO_AUTHORITY: {
        "reserve_limit": Decimal("0"),  # $0
        "payment_limit": Decimal("0"),  # $0
        "description": "No financial authority",
    },
}

INITIAL_RESERVE_CONFIGS = [
    # Auto Claim Configurations
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.BODILY_INJURY,
        "is_required": True,
        "minimum_amount": Decimal("5000"),
        "description": "Required bodily injury reserve for auto claims",
    },
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.PROPERTY_DAMAGE,
        "is_required": True,
        "minimum_amount": Decimal("2500"),
        "description": "Required property damage reserve for auto claims",
    },
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.MEDICAL_PAYMENTS,
        "is_required": False,
        "minimum_amount": Decimal("1000"),
        "description": "Optional medical payments reserve for auto claims",
    },
    # Property Claim Configurations
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.PROPERTY_DAMAGE,
        "is_required": True,
        "minimum_amount": Decimal("5000"),
        "description": "Required property damage reserve",
    },
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.LOSS_OF_USE,
        "is_required": False,
        "minimum_amount": Decimal("1000"),
        "description": "Optional loss of use reserve for property claims",
    },
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.BUSINESS_INTERRUPTION,
        "is_required": False,
        "minimum_amount": None,
        "description": "Optional business interruption reserve for commercial property",
    },
    # General Liability Claim Configurations
    {
        "claim_type": ClaimType.GENERAL_LIABILITY,
        "reserve_type": ReserveType.BODILY_INJURY,
        "is_required": True,
        "minimum_amount": Decimal("10000"),
        "description": "Required bodily injury reserve for GL claims",
    },
    {
        "claim_type": ClaimType.GENERAL_LIABILITY,
        "reserve_type": ReserveType.PROPERTY_DAMAGE,
        "is_required": False,
        "minimum_amount": Decimal("2500"),
        "description": "Optional property damage reserve for GL claims",
    },
    {
        "claim_type": ClaimType.GENERAL_LIABILITY,
        "reserve_type": ReserveType.DEFENSE_COST,
        "is_required": True,
        "minimum_amount": Decimal("5000"),
        "description": "Required defense costs reserve for GL claims",
    },
]


def get_all_permissions() -> Set[str]:
    """Get all defined permissions from role definitions."""
    permissions = set()
    for role_perms in ROLE_PERMISSIONS.values():
        permissions.update(role_perms)
    return permissions


def run_cli_command(command: List[str], cwd: str = None) -> Dict[str, Any]:
    """Execute a CLI command and return the parsed JSON result.
    
    Args:
        command: List of command parts (e.g., ['poetry', 'run', 'clm', 'users', 'create', ...])
        cwd: Working directory for the command. If None, defaults to the CLI directory.
        
    Returns:
        Parsed JSON response from the command
        
    Raises:
        subprocess.CalledProcessError: If command fails
        json.JSONDecodeError: If output is not valid JSON
    """
    # If no cwd specified, default to the CLI directory
    if cwd is None:
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent
        cwd = str(project_root / "cli")
    
    logger.info(f"Executing command: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        
        if result.stdout.strip():
            try:
                # First try to parse the entire output as JSON
                output = result.stdout.strip()
                # Clean up any control characters that might interfere with JSON parsing
                import re
                output = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', output)
                return json.loads(output)
            except json.JSONDecodeError as e:
                # If that fails, try to extract just the JSON part
                logger.debug(f"Full output was not valid JSON, attempting to extract JSON portion")
                
                # Look for JSON object boundaries
                output = result.stdout.strip()
                start_idx = output.find('{')
                if start_idx == -1:
                    logger.error(f"No JSON object found in output: {result.stdout}")
                    raise e
                
                # Find the matching closing brace
                brace_count = 0
                end_idx = start_idx
                for i, char in enumerate(output[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i
                            break
                
                if brace_count != 0:
                    logger.error(f"Unmatched braces in JSON output: {result.stdout}")
                    raise e
                
                # Extract and parse the JSON portion
                json_str = output[start_idx:end_idx + 1]
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as parse_error:
                    logger.error(f"Failed to parse extracted JSON: {json_str}")
                    logger.error(f"Original output: {result.stdout}")
                    raise parse_error
        else:
            logger.warning("Command produced no output")
            return {}
            
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        raise e


def db_reset_and_essential_setup() -> None:
    """Reset database and create only essential configuration.
    
    This function performs a complete database reset and sets up only the
    essential configuration needed for the data populator to work.
    """
    logger.info("🚀 Starting database reset and essential setup...")
    
    # Step 1: Reset database schema
    logger.info("--- Step 1: Resetting database schema ---")
    try:
        # Build psql command using environment variables from settings
        reset_cmd = [
            "psql", 
            "-h", settings.POSTGRES_HOST,
            "-p", str(settings.POSTGRES_PORT),
            "-U", settings.POSTGRES_USER,
            "-d", settings.POSTGRES_DB,
            "-c", "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
        ]
        
        # Set password via environment variable for psql
        env = os.environ.copy()
        env["PGPASSWORD"] = settings.POSTGRES_PASSWORD
        
        logger.info(f"Connecting to database: {settings.POSTGRES_DB} on {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}")
        subprocess.run(reset_cmd, check=True, capture_output=True, text=True, env=env)
        logger.info("✅ Database schema reset successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to reset database schema: {e}")
        logger.error(f"STDERR: {e.stderr}")
        sys.exit(1)
    
    # Step 2: Create schema objects (tables, extensions)
    logger.info("--- Step 2: Creating schema objects ---")
    try:
        engine = create_engine(str(settings.POSTGRES_URL), echo=False)
        create_schema_objects(engine)
        logger.info("✅ Schema objects created successfully")
        engine.dispose()
    except Exception as e:
        logger.error(f"❌ Failed to create schema objects: {e}")
        sys.exit(1)
    
    # Step 3: Insert essential configuration data
    logger.info("--- Step 3: Inserting essential configuration ---")
    try:
        _insert_essential_configuration()
        logger.info("✅ Essential configuration inserted successfully")
    except Exception as e:
        logger.error(f"❌ Failed to insert essential configuration: {e}")
        sys.exit(1)
    
    # Step 4: Set database initialization flag
    logger.info("--- Step 4: Setting database initialization flag ---")
    try:
        _set_database_initialization_flag()
        logger.info("✅ Database initialization flag set successfully")
    except Exception as e:
        logger.error(f"❌ Failed to set database initialization flag: {e}")
        sys.exit(1)
    
    # Step 5: Create controlled superuser
    logger.info("--- Step 5: Creating controlled superuser ---")
    try:
        _create_controlled_superuser()
        logger.info("✅ Controlled superuser created successfully")
    except Exception as e:
        logger.error(f"❌ Failed to create controlled superuser: {e}")
        sys.exit(1)
    
    logger.info("✨ Database reset and essential setup completed successfully!")


def _insert_essential_configuration() -> None:
    """Insert essential configuration data (permissions, authority levels, reserve configs)."""
    engine = create_engine(str(settings.POSTGRES_URL), echo=False, pool_pre_ping=True)
    
    try:
        with Session(engine) as session:
            # Insert permissions
            logger.info("Inserting permissions...")
            existing_permissions = {p.name: p for p in session.scalars(select(Permission)).all()}
            
            # Get all permissions from roles
            all_permissions = get_all_permissions()
            
            # Validate all role permissions are defined
            undefined = all_permissions - PERMISSION_DESCRIPTIONS.keys()
            if undefined:
                # Temporary: Allow MANAGE_TASKS to be undefined if granular exist
                granular_tasks = {
                    "CREATE_TASK", "VIEW_ASSIGNED_TASKS", "VIEW_ALL_TASKS",
                    "EDIT_ASSIGNED_TASKS", "EDIT_ALL_TASKS", "ASSIGN_TASK", "DELETE_TASK",
                }
                truly_undefined = undefined - {"MANAGE_TASKS"}
                if truly_undefined:
                    logger.error(f"❌ Found undefined permissions: {truly_undefined}")
                    sys.exit(1)
                else:
                    logger.info("ℹ️ Ignoring undefined 'MANAGE_TASKS' as granular task permissions are defined.")
            
            # Create new permissions
            for name, description in PERMISSION_DESCRIPTIONS.items():
                if name not in existing_permissions:
                    permission = Permission(name=name, description=description)
                    session.add(permission)
                    logger.info(f"✅ Created permission: {name}")
                else:
                    logger.info(f"ℹ️ Permission already exists: {name}")
            
            # Insert authority levels
            logger.info("Inserting authority levels...")
            existing_authorities = {a.role: a for a in session.scalars(select(AuthorityLevel)).all()}
            
            # Create new authority levels
            for role, data in INITIAL_AUTHORITY_LEVELS.items():
                if role not in existing_authorities:
                    authority = AuthorityLevel(
                        role=role,
                        reserve_limit=data["reserve_limit"],
                        payment_limit=data["payment_limit"],
                        description=data["description"],
                    )
                    session.add(authority)
                    logger.info(f"✅ Created authority level: {role}")
                else:
                    logger.info(f"ℹ️ Authority level already exists: {role}")
            
            # Insert reserve configurations
            logger.info("Inserting reserve configurations...")
            existing_configs = {
                (c.claim_type, c.reserve_type): c 
                for c in session.scalars(select(ReserveConfiguration)).all()
            }
            
            # Create new configurations
            for config_data in INITIAL_RESERVE_CONFIGS:
                key = (config_data["claim_type"], config_data["reserve_type"])
                if key not in existing_configs:
                    config = ReserveConfiguration(**config_data)
                    session.add(config)
                    logger.info(f"✅ Created reserve config: {config.claim_type}:{config.reserve_type}")
                else:
                    logger.info(f"ℹ️ Reserve config already exists: {key[0]}:{key[1]}")
            
            session.commit()
            logger.info("✅ Essential configuration data inserted successfully!")
            
    except Exception as e:
        logger.error(f"❌ Error inserting essential configuration: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def _set_database_initialization_flag() -> None:
    """Set the database initialization flag in system configurations."""
    from claimentine.models.config.system import SystemConfiguration
    
    engine = create_engine(str(settings.POSTGRES_URL), echo=False, pool_pre_ping=True)
    
    try:
        with Session(engine) as session:
            # Check if flag already exists
            existing_config = session.query(SystemConfiguration).filter(
                SystemConfiguration.key == "database_initialized"
            ).first()
            
            if existing_config:
                # Update existing
                existing_config.value = "true"
                existing_config.description = "Flag indicating the database has been initialized"
                logger.info("ℹ️ Updated existing database initialization flag")
            else:
                # Create new
                config = SystemConfiguration(
                    key="database_initialized",
                    value="true",
                    description="Flag indicating the database has been initialized"
                )
                session.add(config)
                logger.info("✅ Created database initialization flag")
            
            session.commit()
            logger.info("✅ Database initialization flag set successfully!")
            
    except Exception as e:
        logger.error(f"❌ Error setting database initialization flag: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def _create_controlled_superuser() -> None:
    """Create a controlled superuser for CLI authentication."""
    email = "<EMAIL>"
    password = "admin"
    first_name = "Admin"
    last_name = "User"
    
    logger.info(f"Creating controlled superuser: {email}")
    
    # Create database engine and session
    engine = create_engine(str(settings.POSTGRES_URL))
    
    try:
        with Session(engine) as session:
            # Check if superuser already exists
            existing_user = session.query(User).filter(User.email == email).first()
            if existing_user:
                logger.info(f"⚠️ Superuser with email {email} already exists!")
                return
            
            # Hash password
            try:
                password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
            except Exception as e:
                logger.error(f"❌ Failed to hash password: {str(e)}")
                return
            
            # Create superuser
            superuser = User(
                email=email,
                first_name=first_name,
                last_name=last_name,
                role=UserRole.ADMIN,
                authority_role=AuthorityRole.UNLIMITED,
                status=UserStatus.ACTIVE,
                password_hash=password_hash,
                email_verified=True,
            )
            
            # Add user first to get an ID
            session.add(superuser)
            session.flush()
            
            # Get all permissions
            all_permissions = session.query(Permission).all()
            if not all_permissions:
                logger.error("❌ No permissions found in database.")
                return
            
            # Create UserPermission associations
            for permission in all_permissions:
                user_permission = UserPermission(
                    user_id=superuser.id,
                    permission_id=permission.id,
                    granted_by=superuser.id,  # Self-granted for superuser
                )
                session.add(user_permission)
            
            # Commit all changes
            session.commit()
            
            logger.info("✅ Controlled superuser created successfully!")
            logger.info(f"Email: {email}")
            logger.info(f"Password: {password}")
            
            # Store superuser info for CLI authentication
            created_data["users"]["admin"] = {
                "id": str(superuser.id),
                "email": email,
                "password": password,
            }
            
    except Exception as e:
        logger.error(f"❌ Failed to create controlled superuser: {str(e)}")
        sys.exit(1)
    finally:
        engine.dispose() 


# Data generation helpers
def generate_realistic_name() -> tuple[str, str]:
    """Generate a realistic first and last name."""
    return fake.first_name(), fake.last_name()


def generate_realistic_email(first_name: str, last_name: str, domain: str = None) -> str:
    """Generate a realistic email address."""
    if domain is None:
        domain = fake.domain_name()
    return f"{first_name.lower()}.{last_name.lower()}@{domain}"


def generate_realistic_phone() -> str:
    """Generate a realistic phone number (max 20 characters)."""
    # Generate a simple US phone number format that's always under 20 chars
    return f"({fake.random_int(100, 999)}) {fake.random_int(100, 999)}-{fake.random_int(1000, 9999)}"


def generate_realistic_address() -> str:
    """Generate a realistic address."""
    return fake.address().replace('\n', ', ')


def generate_realistic_company_name() -> str:
    """Generate a realistic company name."""
    return fake.company()


def generate_realistic_date_range(days_back: int = 365) -> datetime:
    """Generate a realistic date within the specified range."""
    start_date = datetime.now() - timedelta(days=days_back)
    return fake.date_time_between(start_date=start_date, end_date='now')


def generate_realistic_amount(min_amount: float = 1000, max_amount: float = 100000) -> float:
    """Generate a realistic monetary amount."""
    return round(random.uniform(min_amount, max_amount), 2)


def generate_realistic_description(context: str = "incident") -> str:
    """Generate a realistic description for an incident or claim."""
    descriptions = {
        "incident": [
            "Vehicle collision at intersection during rush hour traffic",
            "Property damage due to water leak from overhead pipes", 
            "Customer slip and fall on wet floor in retail location",
            "Equipment malfunction causing production line damage",
            "Vandalism to company vehicle parked overnight",
            "Fire damage to warehouse storage area",
            "Wind damage to building roof during storm",
            "Theft of merchandise from delivery truck",
            "Personal injury during routine maintenance work"
        ],
        "auto": [
            "Rear-end collision at traffic light",
            "Single vehicle accident involving guardrail",
            "Multi-vehicle collision on highway",
            "Parking lot fender bender",
            "Vehicle theft from secure parking garage"
        ],
        "property": [
            "Water damage from burst pipe flooding basement",
            "Fire damage to kitchen area from electrical fault", 
            "Wind damage to roof during severe storm",
            "Vandalism to storefront windows",
            "Theft of computer equipment from office"
        ]
    }
    
    return random.choice(descriptions.get(context, descriptions["incident"]))


def generate_realistic_us_state() -> str:
    """Generate a realistic US state enum value."""
    common_states = [
        "CA", "TX", "FL", "NY", "IL", 
        "PA", "OH", "GA", "NC", "MI",
        "NJ", "VA", "WA", "AZ", "MA",
        "TN", "IN", "MO", "MD", "WI",
        "CO", "MN", "SC", "AL", "LA"
    ]
    return random.choice(common_states)


def generate_realistic_reporter_relationship() -> str:
    """Generate a realistic reporter relationship enum value."""
    relationships = ["INSURED", "CLAIMANT", "ATTORNEY", "AGENT", "OTHER"]
    # Weight towards more common relationships
    weights = [0.4, 0.3, 0.1, 0.15, 0.05]
    return random.choices(relationships, weights=weights)[0]


def generate_realistic_communication_preference() -> str:
    """Generate a realistic communication preference enum value."""
    preferences = ["EMAIL", "PHONE", "TEXT", "MAIL", "PORTAL", "NO_PREFERENCE"]
    # Weight towards more common preferences
    weights = [0.35, 0.25, 0.15, 0.05, 0.15, 0.05]
    return random.choices(preferences, weights=weights)[0]


def generate_realistic_incident_time() -> str:
    """Generate a realistic incident time in HH:MM format."""
    # Generate times during business hours (more common for claims)
    business_hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
    other_hours = list(range(0, 8)) + list(range(18, 24))
    
    # 70% chance of business hours, 30% other
    if random.random() < 0.7:
        hour = random.choice(business_hours)
    else:
        hour = random.choice(other_hours)
    
    minute = random.choice([0, 15, 30, 45])  # Quarter-hour increments are common
    return f"{hour:02d}:{minute:02d}"


def generate_realistic_email_domain() -> str:
    """Generate a realistic email domain for reporters."""
    domains = [
        "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com",
        "icloud.com", "comcast.net", "verizon.net", "att.net", "sbcglobal.net"
    ]
    return random.choice(domains)


# Entity creation functions
def create_user(
    first_name: str,
    last_name: str,
    email: str,
    role: str,
    authority_role: str = "NO_AUTHORITY",
    department: str = None,
    job_title: str = None,
    password: str = "password123"
) -> Dict[str, Any]:
    """Create a user via CLI and return the result."""
    command = [
        "poetry", "run", "clm", "users", "create",
        "--email", email,
        "--password", password,
        "--first-name", first_name,
        "--last-name", last_name,
        "--role", role,
        "--authority-role", authority_role,
        "--output", "json"
    ]
    
    if department:
        command.extend(["--department", department])
    if job_title:
        command.extend(["--job-title", job_title])
    
    result = run_cli_command(command)
    
    # Activate the user immediately after creation
    activate_user(email)
    
    # Store in created_data
    user_key = f"{first_name.lower()}_{last_name.lower()}"
    created_data["users"][user_key] = {
        "id": result["id"],
        "email": email,
        "name": f"{first_name} {last_name}",
        "role": role,
        "authority_role": authority_role
    }
    
    logger.info(f"✅ Created user: {first_name} {last_name} ({email}) - {role}")
    return result


def activate_user(email: str) -> None:
    """Activate a user account by setting status to ACTIVE via CLI."""
    command = [
        "poetry", "run", "clm", "users", "update",
        email,
        "--status", "ACTIVE",
        "--output", "json"
    ]
    
    try:
        run_cli_command(command)
        logger.info(f"✅ Activated user: {email}")
    except Exception as e:
        logger.error(f"❌ Failed to activate user {email}: {e}")
        raise e


def create_customer(name: str, prefix: str, description: str = None) -> Dict[str, Any]:
    """Create a customer via CLI and return the result."""
    command = [
        "poetry", "run", "clm", "customers", "create",
        "--name", name,
        "--prefix", prefix,
        "--output", "json"
    ]
    
    if description:
        command.extend(["--description", description])
    
    result = run_cli_command(command)
    
    # Store in created_data
    created_data["customers"][prefix] = {
        "id": result["id"],
        "name": name,
        "prefix": prefix,
        "description": description
    }
    
    logger.info(f"✅ Created customer: {name} ({prefix})")
    return result


def create_fnol(
    customer_id: str,
    reported_by: str,
    description: str = None,
    incident_date: str = None,
    incident_location: str = None,
    policy_number: str = None,
    # Enhanced fields for ELY-1008
    incident_time: str = None,
    incident_state: str = None,
    reporter_relationship: str = None,
    communication_preference: str = None,
    reporter_phone: str = None,
    reporter_email: str = None
) -> Dict[str, Any]:
    """Create an FNOL via CLI and return the result.
    
    Args:
        customer_id: Customer ID
        reported_by: Name of person reporting
        description: FNOL description
        incident_date: Incident date (YYYY-MM-DD)
        incident_location: Incident location
        policy_number: Policy number
        incident_time: Incident time (HH:MM)
        incident_state: US state where incident occurred (USState enum)
        reporter_relationship: Relationship of reporter (ReporterRelationship enum)
        communication_preference: Preferred communication method
        reporter_phone: Reporter phone number (US format)
        reporter_email: Reporter email address
        
    Note: At least one of reporter_phone or reporter_email must be provided.
    """
    # Validate that at least one contact method is provided
    if not reporter_phone and not reporter_email:
        # Generate realistic contact information if none provided
        reporter_phone = generate_realistic_phone()
    
    command = [
        "poetry", "run", "clm", "fnols", "create",
        "--customer-id", customer_id,
        "--reported-by", reported_by,
        "--output", "json"
    ]
    
    # Add optional parameters
    if description:
        command.extend(["--description", description])
    if incident_date:
        command.extend(["--incident-date", incident_date])
    if incident_location:
        command.extend(["--incident-location", incident_location])
    if policy_number:
        command.extend(["--policy-number", policy_number])
    
    # Enhanced fields for ELY-1008
    if incident_time:
        command.extend(["--incident-time", incident_time])
    if incident_state:
        command.extend(["--incident-state", incident_state])
    if reporter_relationship:
        command.extend(["--reporter-relationship", reporter_relationship])
    if communication_preference:
        command.extend(["--communication-preference", communication_preference])
    if reporter_phone:
        command.extend(["--reporter-phone", reporter_phone])
    if reporter_email:
        command.extend(["--reporter-email", reporter_email])
    
    result = run_cli_command(command)
    
    # Store in created_data
    fnol_key = result["fnol_number"]
    created_data["fnols"][fnol_key] = {
        "id": result["id"],
        "fnol_number": result["fnol_number"],
        "customer_id": customer_id,
        "reported_by": reported_by,
        "reporter_phone": reporter_phone,
        "reporter_email": reporter_email,
        "incident_state": incident_state
    }
    
    logger.info(f"✅ Created FNOL: {result['fnol_number']}")
    return result


def create_note_for_claim(claim_id: str, content: str) -> Dict[str, Any]:
    """Create a note for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "notes", "add",
        "--claim-identifier", claim_id,
        "--content", content,
        "--output", "json"
    ]
    
    result = run_cli_command(command)
    
    # Store in created_data
    if "notes" not in created_data:
        created_data["notes"] = {}
    
    note_key = f"claim_{claim_id}_{len(created_data['notes']) + 1}"
    created_data["notes"][note_key] = {
        "id": result["id"],
        "claim_id": claim_id,
        "content": content[:50] + "..." if len(content) > 50 else content
    }
    
    logger.info(f"✅ Created note for claim {claim_id}")
    return result


def create_task_for_claim(
    claim_id: str,
    title: str,
    description: str,
    priority: str = "MEDIUM",
    assignee_id: str = None,
    **kwargs
) -> Dict[str, Any]:
    """Create a task for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "tasks", "create",
        "--claim-identifier", claim_id,
        "--title", title,
        "--description", description,
        "--priority", priority,
        "--output", "json"
    ]
    
    if assignee_id:
        command.extend(["--assignee", assignee_id])
    
    # Add optional parameters
    optional_params = ["status", "due_date"]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            command.extend([cli_param, str(kwargs[param])])
    
    result = run_cli_command(command)
    
    # Store in created_data
    if "tasks" not in created_data:
        created_data["tasks"] = {}
    
    task_key = f"claim_{claim_id}_{title.replace(' ', '_').lower()}"
    created_data["tasks"][task_key] = {
        "id": result["id"],
        "claim_id": claim_id,
        "title": title,
        "assignee_id": assignee_id
    }
    
    logger.info(f"✅ Created task: {title} for claim {claim_id}")
    return result


# Common entity creation functions
def create_common_users() -> None:
    """Create a set of common users for the system."""
    logger.info("Creating common users...")
    
    # Create adjusters
    adjusters = [
        ("Sarah", "Johnson", "ADJUSTER", "INTERMEDIATE", "Claims", "Senior Claims Adjuster"),
        ("Michael", "Chen", "ADJUSTER", "SENIOR", "Claims", "Lead Claims Adjuster"),
        ("Emily", "Rodriguez", "ADJUSTER", "BASIC", "Claims", "Claims Adjuster"),
        ("David", "Thompson", "ADJUSTER", "INTERMEDIATE", "Claims", "Auto Claims Specialist"),
        ("Lisa", "Anderson", "ADJUSTER", "SENIOR", "Claims", "Property Claims Specialist"),
    ]
    
    for first_name, last_name, role, authority, dept, job_title in adjusters:
        email = generate_realistic_email(first_name, last_name, "claimentine.com")
        create_user(first_name, last_name, email, role, authority, dept, job_title)
    
    # Create managers
    managers = [
        ("Robert", "Wilson", "MANAGER", "MANAGER", "Claims", "Claims Manager"),
        ("Jennifer", "Davis", "MANAGER", "SUPERVISOR", "Claims", "Regional Claims Manager"),
    ]
    
    for first_name, last_name, role, authority, dept, job_title in managers:
        email = generate_realistic_email(first_name, last_name, "claimentine.com")
        create_user(first_name, last_name, email, role, authority, dept, job_title)


def create_common_customers() -> None:
    """Create a set of common customers for the system."""
    logger.info("Creating common customers...")
    
    customers = [
        ("Acme Manufacturing Corp", "ACME", "Large manufacturing company specializing in industrial equipment"),
        ("Sunshine Retail Group", "SUNR", "Regional retail chain with 50+ locations"),
        ("Metro Construction LLC", "METR", "Commercial construction and development company"),
        ("TechStart Solutions", "TECH", "Technology startup providing software solutions"),
        ("Green Valley Farms", "GVFM", "Agricultural business with multiple farm locations"),
        ("Coastal Shipping Co", "COST", "Maritime shipping and logistics company"),
        ("Downtown Restaurant Group", "DTRG", "Restaurant chain with urban locations"),
        ("Precision Auto Parts", "PAPT", "Automotive parts manufacturer and distributor"),
    ]
    
    for name, prefix, description in customers:
        create_customer(name, prefix, description)


def authenticate_cli() -> None:
    """Authenticate the CLI with the admin user."""
    logger.info("Authenticating CLI with admin user...")
    
    admin_data = created_data["users"]["admin"]
    email = admin_data["email"]
    password = admin_data["password"]
    
    # Get the project root directory (where the script is located relative to cli/)
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent
    cli_dir = project_root / "cli"
    
    command = [
        "poetry", "run", "clm", "auth", "login",
        "--email", email,
        "--password", password
    ]
    
    try:
        result = subprocess.run(
            command,
            cwd=str(cli_dir),
            capture_output=True,
            text=True,
            check=True
        )
        logger.info("✅ CLI authentication successful")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ CLI authentication failed: {e}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        raise e


def create_claim(
    claim_type: str,
    customer_id: str,
    description: str,
    claimant_name: str,
    **kwargs
) -> Dict[str, Any]:
    """Create a claim via CLI and return the result.
    
    Args:
        claim_type: Type of claim (AUTO, PROPERTY, GENERAL_LIABILITY)
        customer_id: Customer ID
        description: Claim description
        claimant_name: Name of the claimant
        **kwargs: Additional claim-specific parameters including:
            - reporter_phone: Reporter phone number (US format)
            - reporter_email: Reporter email address
            - jurisdiction: US state jurisdiction (USState enum)
            - And all other claim fields
            
    Note: At least one of reporter_phone or reporter_email must be provided for claim creation.
    """
    # Ensure at least one reporter contact method is provided
    if not kwargs.get("reporter_phone") and not kwargs.get("reporter_email"):
        # Generate realistic contact information if none provided
        kwargs["reporter_phone"] = generate_realistic_phone()
    
    command = [
        "poetry", "run", "clm", "claims", "create",
        "--type", claim_type,
        "--customer-id", customer_id,
        "--description", description,
        "--claimant-name", claimant_name,
        "--output", "json"
    ]
    
    # Add optional parameters (including new reporter contact fields)
    optional_params = [
        "claimant_email", "claimant_phone", "incident_date", "loss_date",
        "incident_location", "jurisdiction", "assigned_to", "supervisor",
        "policy_number", 
        # Enhanced fields for ELY-1008
        "reporter_phone", "reporter_email",
        # Vehicle/Auto fields
        "vehicle_vin", "vehicle_make", "vehicle_model",
        "vehicle_year", "driver_name", "driver_license", "point_of_impact",
        "incident_type", "collision_type", "passenger_count", "passenger_details",
        "cargo_theft", "cargo_description", "has_property_damage",
        "property_damage_description", "property_damage_type", 
        "property_damage_address", "property_damage_owner", "property_damage_value",
        # Property fields
        "property_type", "property_address", "damage_type", "estimated_value",
        # General Liability fields
        "gl_incident_type", "liability_type", "owner_name", "owner_address",
        "manufacturer_name", "product_type", "work_type", "injury_nature"
    ]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            # Convert underscores to hyphens for CLI parameters
            cli_param = "--" + param.replace("_", "-")
            value = kwargs[param]
            
            # Handle boolean values
            if isinstance(value, bool):
                if value:
                    command.append(cli_param)
            else:
                command.extend([cli_param, str(value)])
    
    result = run_cli_command(command)
    
    # Store in created_data
    claim_key = result["claim_number"]
    created_data["claims"][claim_key] = {
        "id": result["id"],
        "claim_number": result["claim_number"],
        "type": claim_type,
        "customer_id": customer_id,
        "claimant_name": claimant_name,
        "reporter_phone": kwargs.get("reporter_phone"),
        "reporter_email": kwargs.get("reporter_email"),
        "jurisdiction": kwargs.get("jurisdiction")
    }
    
    logger.info(f"✅ Created {claim_type} claim: {result['claim_number']}")
    return result


def create_claim_financials(
    claim_id: str,
    estimated_value: float,
    reserves: List[Dict[str, Any]] = None,
    **kwargs
) -> Dict[str, Any]:
    """Create financial details for a claim via CLI.
    
    Args:
        claim_id: Claim ID or number
        estimated_value: Estimated total value of the claim
        reserves: List of reserves with 'type' and 'amount' keys
        **kwargs: Additional financial parameters
    """
    command = [
        "poetry", "run", "clm", "claims", "financials", "create",
        claim_id,
        "--estimated-value", str(estimated_value),
        "--output", "json"
    ]
    
    # Add reserves if provided
    if reserves:
        for reserve in reserves:
            command.extend(["--reserve-type", reserve["type"]])
            command.extend(["--reserve-amount", str(reserve["amount"])])
    
    # Add optional financial parameters
    optional_params = [
        "currency", "indemnity_paid", "expense_paid", "defense_paid",
        "recovery_expected", "recovery_received", "deductible", "coverage_limit"
    ]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            command.extend([cli_param, str(kwargs[param])])
    
    result = run_cli_command(command)
    logger.info(f"✅ Created financials for claim {claim_id}")
    return result


def add_reserve_to_claim(
    claim_id: str,
    reserve_type: str,
    amount: float,
    notes: str = None
) -> Dict[str, Any]:
    """Add or update a reserve for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "claims", "financials", "update-reserve",
        claim_id,
        "--reserve-type", reserve_type,
        "--amount", str(amount)
    ]
    
    if notes:
        command.extend(["--notes", notes])
    
    result = run_cli_command(command)
    logger.info(f"✅ Added {reserve_type} reserve of ${amount} to claim {claim_id}")
    return result


def add_payment_to_claim(
    claim_id: str,
    payment_type: str,
    amount: float,
    payee: str,
    payment_date: str,
    notes: str = None
) -> Dict[str, Any]:
    """Add a payment to a claim via CLI."""
    command = [
        "poetry", "run", "clm", "claims", "financials", "add-payment",
        claim_id,
        "--payment-type", payment_type,
        "--amount", str(amount),
        "--payee", payee,
        "--payment-date", payment_date
    ]
    
    if notes:
        command.extend(["--notes", notes])
    
    result = run_cli_command(command)
    logger.info(f"✅ Added {payment_type} payment of ${amount} to {payee} for claim {claim_id}")
    return result


def create_injured_person_for_claim(
    claim_id: str,
    name: str,
    person_type: str,
    contact_info: str,
    incident_report_status: str = "FILED",
    **kwargs
) -> Dict[str, Any]:
    """Create an injured person for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "claims", "create-injured-person",
        claim_id,
        "--name", name,
        "--person-type", person_type,
        "--contact-info", contact_info,
        "--incident-report-status", incident_report_status,
        "--output", "json"
    ]
    
    # Add optional parameters
    optional_params = [
        "incident_location", "incident_description", "age",
        "report_filer_name", "report_filer_contact"
    ]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            command.extend([cli_param, str(kwargs[param])])
    
    result = run_cli_command(command)
    logger.info(f"✅ Created injured person: {name} for claim {claim_id}")
    return result


def create_injury_for_injured_person(
    claim_id: str,
    person_id: str,
    injury_description: str,
    injury_type: str,
    injury_severity: str,
    medical_treatment_requirements: str = "NONE",
    **kwargs
) -> Dict[str, Any]:
    """Create an injury for an injured person via CLI."""
    command = [
        "poetry", "run", "clm", "claims", "create-injury",
        claim_id,
        person_id,
        "--injury-description", injury_description,
        "--injury-type", injury_type,
        "--injury-severity", injury_severity,
        "--medical-treatment-requirements", medical_treatment_requirements,
        "--output", "json"
    ]
    
    # Add optional parameters
    optional_params = [
        "equipment_involved", "equipment_details", "equipment_owned_by_insured",
        "safety_measures_involved", "safety_measures_description",
        "treatment_nature", "medical_provider_name", "medical_provider_address",
        "estimated_cost", "insurance_billing_status"
    ]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            value = kwargs[param]
            
            # Handle boolean values
            if isinstance(value, bool):
                if value:
                    command.append(cli_param)
            else:
                command.extend([cli_param, str(value)])
    
    result = run_cli_command(command)
    logger.info(f"✅ Created injury: {injury_type} for person {person_id} in claim {claim_id}")
    return result


def create_witness_for_claim(claim_id: str, name: str, contact_info: str, **kwargs) -> Dict[str, Any]:
    """Create a witness for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "witnesses", "add",
        claim_id,
        "--name", name,
        "--output", "json"
    ]
    
    # Parse contact_info to extract email and phone
    if "," in contact_info:
        parts = [part.strip() for part in contact_info.split(",")]
        email = parts[0] if "@" in parts[0] else None
        phone = parts[1] if len(parts) > 1 else None
    else:
        if "@" in contact_info:
            email = contact_info
            phone = None
        else:
            email = None
            phone = contact_info
    
    if email:
        command.extend(["--email", email])
    if phone:
        command.extend(["--phone", phone])
    
    # Add optional parameters
    optional_params = ["statement", "address"]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            command.extend([cli_param, str(kwargs[param])])
    
    result = run_cli_command(command)
    
    # Store in created_data
    if "witnesses" not in created_data:
        created_data["witnesses"] = {}
    
    witness_key = f"claim_{claim_id}_{name.replace(' ', '_').lower()}"
    created_data["witnesses"][witness_key] = {
        "id": result["id"],
        "claim_id": claim_id,
        "name": name
    }
    
    logger.info(f"✅ Created witness: {name} for claim {claim_id}")
    return result


def create_attorney_for_claim(claim_id: str, name: str, contact_info: str, attorney_type: str = "DEFENSE", **kwargs) -> Dict[str, Any]:
    """Create an attorney for a claim via CLI."""
    command = [
        "poetry", "run", "clm", "attorneys", "add",
        claim_id,
        "--name", name,
        "--attorney-type", attorney_type,
        "--output", "json"
    ]
    
    # Parse contact_info to extract email and phone
    if "," in contact_info:
        parts = [part.strip() for part in contact_info.split(",")]
        email = parts[0] if "@" in parts[0] else None
        phone = parts[1] if len(parts) > 1 else None
    else:
        if "@" in contact_info:
            email = contact_info
            phone = None
        else:
            email = None
            phone = contact_info
    
    if email:
        command.extend(["--email", email])
    if phone:
        command.extend(["--phone", phone])
    
    # Add optional parameters
    optional_params = ["firm_name", "address", "notes"]
    
    for param in optional_params:
        if param in kwargs and kwargs[param] is not None:
            cli_param = "--" + param.replace("_", "-")
            command.extend([cli_param, str(kwargs[param])])
    
    result = run_cli_command(command)
    
    # Store in created_data
    if "attorneys" not in created_data:
        created_data["attorneys"] = {}
    
    attorney_key = f"claim_{claim_id}_{name.replace(' ', '_').lower()}"
    created_data["attorneys"][attorney_key] = {
        "id": result["id"],
        "claim_id": claim_id,
        "name": name
    }
    
    logger.info(f"✅ Created attorney: {name} for claim {claim_id}")
    return result 