{"get": {"tags": ["claims", "claims"], "summary": "Get Claim Financials", "description": "Get financials for a claim identified by ID or Number.", "operationId": "get_claim_financials_api_v1_claims__claim_identifier__financials_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "../components/schemas/ClaimFinancialsInDB.json"}, {"type": "null"}], "title": "Response Get Claim Financials Api V1 Claims  Claim Identifier  Financials Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "claims"], "summary": "Create Claim Financials", "description": "Create financials for a claim identified by ID or Number.", "operationId": "create_claim_financials_api_v1_claims__claim_identifier__financials_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimFinancialsCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimFinancialsInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "put": {"tags": ["claims", "claims"], "summary": "Update Claim Financials", "description": "Update financial details for a claim identified by ID or Number.", "operationId": "update_claim_financials_api_v1_claims__claim_identifier__financials_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimFinancialsUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimFinancialsInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}