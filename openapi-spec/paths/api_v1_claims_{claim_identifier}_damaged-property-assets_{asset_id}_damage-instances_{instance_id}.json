{"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damage Instance", "description": "Get a damage instance for a damaged property asset.", "operationId": "get_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DamageInstanceResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "put": {"tags": ["claims", "claims", "property-damage"], "summary": "Update Damage Instance", "description": "Update a damage instance for a damaged property asset.", "operationId": "update_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__put", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/DamageInstanceUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DamageInstanceResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "claims", "property-damage"], "summary": "Delete Damage Instance", "description": "Delete a damage instance for a damaged property asset.", "operationId": "delete_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}