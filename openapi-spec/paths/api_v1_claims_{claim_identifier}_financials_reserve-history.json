{"get": {"tags": ["claims", "claims"], "summary": "Get Claim Reserve History", "description": "Get reserve history for a claim identified by ID or Number.", "operationId": "get_claim_reserve_history_api_v1_claims__claim_identifier__financials_reserve_history_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/ReserveHistoryInDB.json"}, "title": "Response Get Claim Reserve History Api V1 Claims  Claim Identifier  Financials Reserve History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}