{"get": {"tags": ["claims", "claims"], "summary": "<PERSON>", "description": "Get claim by ID (UUID) or Claim Number.\n\nThe response model will be determined by the claim's type.\nOptionally include related items via the 'include' query parameter:\n- customer: Claim customer (included by default if not specified otherwise)\n- documents: Claim documents\n- notes: Claim notes\n- tasks: Claim tasks\n- status_history: Claim status history\n- financials: Claim financials\n- assigned_to: Assigned user details\n- supervisor: Supervisor user details\n- created_by: Creator user details\n- auto_details: Auto claim details (for auto claims)\n- property_details: Property claim details (for property claims)\n- gl_details: General liability claim details (for general liability claims)", "operationId": "get_claim_api_v1_claims__claim_identifier__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "../components/schemas/AutoClaimResponse.json"}, {"$ref": "../components/schemas/PropertyClaimResponse.json"}, {"$ref": "../components/schemas/GeneralLiabilityClaimResponse.json"}], "title": "Response Get Claim Api V1 Claims  Claim Identifier  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["claims", "claims"], "summary": "Update Claim", "description": "Update claim by ID or Number.", "operationId": "update_claim_api_v1_claims__claim_identifier__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID (UUID) or Claim Number", "title": "Claim Identifier"}, "description": "Claim ID (UUID) or Claim Number"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (documents, notes, tasks, status_history, auto_details, property_details)", "default": ["status_history"], "title": "Include"}, "description": "Include related items (documents, notes, tasks, status_history, auto_details, property_details)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "../components/schemas/AutoClaimUpdate.json"}, {"$ref": "../components/schemas/PropertyClaimUpdate.json"}, {"$ref": "../components/schemas/GeneralLiabilityClaimUpdate.json"}], "title": "Claim Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "../components/schemas/AutoClaimResponse.json"}, {"$ref": "../components/schemas/PropertyClaimResponse.json"}, {"$ref": "../components/schemas/GeneralLiabilityClaimResponse.json"}], "title": "Response Update Claim Api V1 Claims  Claim Identifier  Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "claims"], "summary": "Delete Claim", "description": "Delete claim by ID or Number.", "operationId": "delete_claim_api_v1_claims__claim_identifier__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}