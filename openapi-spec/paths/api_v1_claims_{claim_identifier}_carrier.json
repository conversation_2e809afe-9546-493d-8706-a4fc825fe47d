{"patch": {"tags": ["claims", "claims", "recovery"], "summary": "Update Claim Carrier Details", "description": "Update third-party carrier information for a claim identified by ID or Number.", "operationId": "update_claim_carrier_details_api_v1_claims__claim_identifier__carrier_patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "carrier_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Name of the third-party carrier", "title": "Carrier Name"}, "description": "Name of the third-party carrier"}, {"name": "carrier_contact", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Contact info for the third-party carrier", "title": "Carrier Contact"}, "description": "Contact info for the third-party carrier"}, {"name": "carrier_claim_number", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Third-party carrier's claim number", "title": "Carrier Claim Number"}, "description": "Third-party carrier's claim number"}, {"name": "carrier_adjuster", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Third-party carrier's adjuster name", "title": "Carrier Adjuster"}, "description": "Third-party carrier's adjuster name"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimResponseSchema.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}