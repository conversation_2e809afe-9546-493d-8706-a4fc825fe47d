{"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injury", "description": "Get an injury for an injured person.", "operationId": "get_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuryResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "put": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Update Injury", "description": "Update an injury for an injured person.", "operationId": "update_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuryUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuryResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Delete Injury", "description": "Delete an injury for an injured person.", "operationId": "delete_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["gl_details", "customer", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}